from rest_framework import serializers
from core.models import ContactChannel


class CrmContactChannelSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContactChannel
        fields = (
            "lsid",
            "name",
            "description",
            "created_at",
            "updated_at",
        )


class CrmCreateContactChannelSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContactChannel
        fields = [
            "name",
            "description",
        ]

    def validate_name(self, value):
        value = value.strip()
        if ContactChannel.objects.filter(name__iexact=value).exists():
            raise serializers.ValidationError(
                "Este registro ya existe. Si no lo encuentras, contacta con el administrador para restaurarlo."
            )
        return value

    def create(self, validated_data):
        return ContactChannel.objects.create(**validated_data)

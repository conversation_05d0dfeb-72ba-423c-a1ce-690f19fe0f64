from django_filters import rest_framework as filters
from core.models import StudentEnrollment


class StudentEnrollmentFilter(filters.FilterSet):
    offering = filters.CharFilter(method="filter_offering")

    def filter_offering(self, queryset, name, value):
        if value:
            # Split by comma and strip spaces
            offering_ids = [v.strip() for v in value.split(",")]
            return queryset.filter(order_item__offering__in=offering_ids)
        return queryset

    class Meta:
        model = StudentEnrollment
        fields = ["offering"]

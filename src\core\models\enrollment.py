import uuid
from django.db import models
from .base import AuditBaseModel


class StudentEnrollment(AuditBaseModel):
    """
    Model representing a student's enrollment in a program offering.
    """

    eid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    order_item = models.OneToOneField(
        "core.OrderItem",
        on_delete=models.CASCADE,
        related_name="enrollment",
        verbose_name="Order Item",
    )
    user = models.ForeignKey(
        "core.User",
        on_delete=models.CASCADE,
        related_name="enrollments",
        verbose_name="User",
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name="Is Active",
    )
    certificate_issued = models.BooleanField(
        default=False,
        verbose_name="Certificate Issued",
    )
    certificate_sent = models.BooleanField(
        default=False,
        verbose_name="Certificate Sent",
    )
    certificate = models.OneToOneField(
        "core.Credential",
        on_delete=models.SET_NULL,
        related_name="enrollment",
        blank=True,
        null=True,
        verbose_name="Certificate",
    )

    class Meta:
        verbose_name = "Student Enrollment"
        verbose_name_plural = "Student Enrollments"
        unique_together = ["order_item", "user"]

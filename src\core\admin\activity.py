from django.contrib import admin
from core.models import Activity


@admin.register(Activity)
class ActivityAdmin(admin.ModelAdmin):
    readonly_fields = [
        "created_by",
        "created_at",
        "updated_by",
        "updated_at",
        "deleted_by",
        "deleted_at",
    ]

    list_display = [
        "title",
        "status",
        "deadline",
    ]
    list_filter = ("responsible", "status")
    search_fields = (
        "aid",
        "title",
        "responsible__first_name",
        "responsible__last_name",
    )

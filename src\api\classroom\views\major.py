from rest_framework import viewsets
from core.models import Major
from api.classroom.serializers.auth import MajorSelectSerializer
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import TokenAuthentication


class MajorViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Major.objects.filter(deleted=False)
    serializer_class = MajorSelectSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

import os
from django.conf import settings
from django.core.mail import send_mail
from django.utils.encoding import force_bytes, force_str
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from api.exceptions import InvalidCredentials
from api.mixins import SwaggerTagMixin
from api.shared.serializers.auth import TokenSerializer
from api.website.serializers.auth import (
    WebsiteAuthLoginSerializer,
    WebsiteAuthRegisterSerializer,
    ResetPasswordRequestSerializer,
    ResetPasswordConfirmSerializer,
)
from api.website.lib.tokens import TenMinuteResetTokenGenerator
from api.website.tasks.notification import send_welcome_email
from core.models import User


class WebsiteAuthViewSet(viewsets.GenericViewSet, SwaggerTagMixin):
    """
    Authentication ViewSet
    """

    queryset = User.objects.filter(deleted=False)
    serializer_class = WebsiteAuthLoginSerializer
    swagger_tags = ["Auth"]
    manual_parameters = [
        openapi.Parameter(
            "token",
            openapi.IN_QUERY,
            description="Reset token",
            type=openapi.TYPE_STRING,
            required=True,
        ),
        openapi.Parameter(
            "pair",
            openapi.IN_QUERY,
            description="Encoded user UID (uidb64)",
            type=openapi.TYPE_STRING,
            required=True,
        ),
    ]

    token_generator = TenMinuteResetTokenGenerator()

    @swagger_auto_schema(
        methods=["POST"],
        responses={
            status.HTTP_200_OK: TokenSerializer(),
        },
    )
    @action(detail=False, methods=["POST"])
    def login(self, request, *args, **kwargs):
        """
        Login user and return auth token
        """
        serializer = self.get_serializer(
            data=request.data, context={"request": request}
        )

        try:
            serializer.is_valid(raise_exception=True)
            user = serializer.validated_data["user"]
        except Exception:
            raise InvalidCredentials(detail="Correo o contraseña incorrectos.")
        token, _ = Token.objects.get_or_create(user=user)
        return Response(TokenSerializer(token).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        methods=["POST"],
        responses={
            status.HTTP_200_OK: TokenSerializer(),
        },
    )
    @action(detail=False, methods=["POST"])
    def register(self, request, *args, **kwargs):
        """
        Register user and return auth token
        """
        serializer = WebsiteAuthRegisterSerializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        try:
            send_welcome_email.delay(
                user_uid=user.uid,
                email=user.email,
            )
        except Exception as e:
            print(f"Error queueing the task: {str(e)}")
        token, _ = Token.objects.get_or_create(user=user)
        return Response(TokenSerializer(token).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        methods=["POST"],
        request_body=ResetPasswordRequestSerializer,
        responses={status.HTTP_200_OK: "Reset link sent"},
    )
    @action(
        detail=False,
        methods=["POST"],
        permission_classes=[permissions.AllowAny],
        url_path="request-password-reset",
    )
    def request_password_reset(self, request):
        """
        Sends reset link to email if user exists
        """
        serializer = ResetPasswordRequestSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        email = serializer.validated_data["email"]
        user = User.objects.filter(email=email, deleted=False).first()
        if not user:
            return Response(
                {"detail": "Email incorrecto. Inténtelo otra vez."},
                status=404,
            )

        token = self.token_generator.make_token(user)
        uidb64 = urlsafe_base64_encode(force_bytes(user.pk))
        reset_url = os.environ.get("APP_HOST")
        reset_link = f"{reset_url}/auth/renew-password/{token}?pair={uidb64}"

        message = (
            f"Hola {user.first_name},\n\n"
            f"Recibimos una solicitud para restablecer tu contraseña. "
            f"Puedes hacerlo desde el siguiente enlace:\n\n{reset_link}\n\n"
            f"Este enlace es válido por un tiempo limitado."
        )

        try:
            send_mail(
                "Restablecimiento de contraseña",
                message,
                settings.DEFAULT_FROM_EMAIL,
                [user.email],
            )
        except Exception:
            return Response({"detail": "Error sending email"}, status=500)

        return Response({"detail": "Password reset link sent to email"})

    @swagger_auto_schema(
        methods=["GET"],
        manual_parameters=manual_parameters,
        responses={
            200: openapi.Response(description="Valid token"),
            400: openapi.Response(description="Invalid token"),
            404: openapi.Response(description="User not found"),
        },
    )
    @action(
        detail=False,
        methods=["GET"],
        permission_classes=[permissions.AllowAny],
        url_path="validate-reset-token",
    )
    def validate_reset_token(self, request):
        """
        Validates reset token
        """
        token = request.query_params.get("token")
        uidb64 = request.query_params.get("pair")

        try:
            user_id = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.filter(pk=user_id, deleted=False).first()
        except Exception:
            user = None

        if not user:
            return Response({"detail": "User not found"}, status=404)

        if self.token_generator.check_token(user, token):
            return Response({"detail": "Valid token"})
        return Response({"detail": "Invalid or expired token"}, status=400)

    @swagger_auto_schema(
        methods=["PATCH"],
        manual_parameters=manual_parameters,
        request_body=ResetPasswordConfirmSerializer,
        responses={
            200: TokenSerializer(),
            400: "Invalid data",
            401: openapi.Response(
                description="Invalid or expired token",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={"detail": openapi.Schema(type=openapi.TYPE_STRING)},
                ),
            ),
            404: "User not found",
        },
    )
    @action(
        detail=False,
        methods=["PATCH"],
        permission_classes=[permissions.AllowAny],
        url_path="confirm-password-reset",
        serializer_class=ResetPasswordConfirmSerializer,
    )
    def confirm_password_reset(self, request):
        """
        Resets user password using token and new password
        """
        token = request.query_params.get("token")
        uidb64 = request.query_params.get("pair")

        if not token or not uidb64:
            return Response({"detail": "Missing parameters"}, status=400)

        try:
            user_id = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.filter(pk=user_id, deleted=False).first()
        except Exception:
            user = None

        if not user:
            return Response({"detail": "User not found"}, status=404)

        if not self.token_generator.check_token(user, token):
            return Response({"detail": "Invalid or expired token"}, status=401)

        serializer = ResetPasswordConfirmSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user.set_password(serializer.validated_data["new_password"])
        user.save()

        token, _ = Token.objects.get_or_create(user=user)
        return Response(TokenSerializer(token).data)

    @swagger_auto_schema(
        methods=["POST"],
        responses={
            status.HTTP_200_OK: TokenSerializer(),
        },
    )
    @action(
        detail=False,
        methods=["POST"],
        url_path="check-token",
        authentication_classes=[TokenAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def check_token(self, request, *args, **kwargs):
        """
        Check if the provided token is valid
        Token should be sent in the Authorization header as 'Token <token_key>'
        """
        user = request.user
        token_obj = Token.objects.get(user=user)

        return Response(TokenSerializer(token_obj).data, status=status.HTTP_200_OK)

    @swagger_auto_schema(
        methods=["POST"],
        responses={
            status.HTTP_204_NO_CONTENT: "Token invalidated successfully.",
            status.HTTP_401_UNAUTHORIZED: "Invalid token or user not authenticated.",
        },
    )
    @action(
        detail=False,
        methods=["POST"],
        url_path="logout",
        authentication_classes=[TokenAuthentication],
        permission_classes=[IsAuthenticated],
    )
    def logout(self, request, *args, **kwargs):
        """
        Logout user by invalidating the token
        Token should be sent in the Authorization header as 'Token <token_key>'
        """
        user = request.user
        token_obj = Token.objects.get(user=user)
        token_obj.delete()

        return Response(status=status.HTTP_204_NO_CONTENT)

from rest_framework import routers
from api.crm.views import (
    template as template_views,
    template_variable as template_variable_views,
    template_type as template_type_views,
    event_reminder as event_reminder_views,
)
from api.crm.views import contact as contact_views
from api.crm.views import educational_institution as EducationalInstitutionViews
from api.crm.views import major as major_views
from api.crm.views import term as term_views
from api.crm.views import order as order_views
from api.crm.views import order_item as order_item_views
from api.crm.views import payment as payment_views
from api.crm.views import payment_method as payment_method_views
from api.crm.views import activity as activity_views
from api.crm.views import staff as staff_views
from api.crm.views import offering as offering_views
from api.crm.views import benefit as benefit_views
from api.crm.views import event as event_views
from api.crm.views import contact_channel as contact_channel_views
from api.crm.views import lead_origin as lead_origin_views
from api.crm.views import event_schedule as event_schedule_views
from api.crm.views.partnership import CrmPartnershipViewSet
from api.crm.views.dashboard.contact import CrmDashboardContactViewSet
from api.crm.views.dashboard.sales import CrmDashboardSalesViewSet
from api.crm.views.dashboard.payment import CrmDashboardPaymentViewSet
from api.crm.views.dashboard.event import CrmDashboardEventViewSet

router = routers.DefaultRouter(trailing_slash=False)

router.register(
    r"templates",
    template_views.CrmTemplateViewSet,
    basename="templates",
)

router.register(
    r"template-variables",
    template_variable_views.CrmTemplateVariableViewSet,
    basename="template-variables",
)

router.register(
    r"template-types",
    template_type_views.CrmTemplateTypeViewSet,
    basename="template-types",
)

router.register(
    r"contacts",
    contact_views.CrmContactViewSet,
    basename="crm-contacts",
)
router.register(
    r"educational-institutions",
    EducationalInstitutionViews.CrmEducationalInstitutionViewSet,
    basename="crm-educational-institutions",
)
router.register(
    r"partnerships",
    CrmPartnershipViewSet,
    basename="crm-partnerships",
)
router.register(
    r"majors",
    major_views.CrmMajorViewSet,
    basename="crm-majors",
)
router.register(
    r"payment-methods",
    payment_method_views.CrmPaymentMethodViewSet,
    basename="crm-payment-methods",
)
router.register(
    r"terms",
    term_views.CrmTermViewSet,
    basename="crm-terms",
)

router.register(
    r"orders",
    order_views.CrmOrderViewSet,
    basename="crm-orders",
)

router.register(
    r"order-items",
    order_item_views.CrmOrderItemViewSet,
    basename="crm-order-items",
)

router.register(
    r"contact-channels",
    contact_channel_views.CrmContactChannelViewSet,
    basename="crm-contact-channels",
)
router.register(
    r"lead-origins",
    lead_origin_views.CrmLeadOriginViewSet,
    basename="crm-lead-origins",
)

router.register(
    r"staff",
    staff_views.CrmStaffViewSet,
    basename="crm-staff",
)

router.register(
    r"payments",
    payment_views.CrmPaymentViewSet,
    basename="crm-payments",
)

router.register(
    r"activities",
    activity_views.CrmActivityViewSet,
    basename="crm-activities",
)

router.register(
    r"event-reminders",
    event_reminder_views.CrmEventReminderViewSet,
    basename="event-reminders",
)

router.register(
    r"offerings",
    offering_views.CrmOfferingViewSet,
    basename="crm-offerings",
)

router.register(
    r"events",
    event_views.CrmEventViewSet,
    basename="crm-events",
)

router.register(
    r"event-schedules",
    event_schedule_views.CrmEventScheduleViewSet,
    basename="crm-event-schedules",
)

router.register(
    r"benefits",
    benefit_views.CrmBenefitViewSet,
    basename="crm-benefits",
)

router.register(
    r"dashboard/contacts",
    CrmDashboardContactViewSet,
    basename="crm-dashboard-contacts",
)

router.register(
    r"dashboard/sales",
    CrmDashboardSalesViewSet,
    basename="crm-dashboard-sales",
)

router.register(
    r"dashboard/payments",
    CrmDashboardPaymentViewSet,
    basename="crm-dashboard-payments",
)

router.register(
    r"dashboard/events",
    CrmDashboardEventViewSet,
    basename="crm-dashboard-events",
)

urlpatterns = [
    *router.urls,
]

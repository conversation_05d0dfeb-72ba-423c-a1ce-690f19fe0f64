import uuid
from django.db import models
from core.models.base import AuditBaseModel, OrderBaseModel
from django.utils.functional import cached_property
from django.core.exceptions import ValidationError


class Offering(AuditBaseModel, OrderBaseModel):
    REMOTE_MODALITY = "REMOTE"
    IN_PERSON_MODALITY = "IN_PERSON"

    IN_LIVE_FORMAT = "LIVE"
    ASYNCHRONOUS_FORMAT = "ASYNCHRONOUS"

    SPECIALIZATION_TYPE = "SPECIALIZATION"
    PREPARATION_TYPE = "PREPARATION"
    REVIEW_WORKSHOP_TYPE = "REVIEW_WORKSHOP"
    UNDERGRADUATE_FORMATION = "UNDERGRADUATE_FORMATION"

    PLANNING_STAGE = "PLANNING"
    LAUNCHED_STAGE = "LAUNCHED"
    ENROLLMENT_STAGE = "ENROLLMENT"
    ENROLLMENT_CLOSED_STAGE = "ENROLLMENT_CLOSED"
    FINISHED_STAGE = "FINISHED"

    MODALITY_CHOICES = [
        (REMOTE_MODALITY, "Remote"),
        (IN_PERSON_MODALITY, "In-Person"),
    ]

    TYPE_CHOICES = [
        (SPECIALIZATION_TYPE, "Specialization"),
        (PREPARATION_TYPE, "Preparation"),
        (REVIEW_WORKSHOP_TYPE, "Review Workshop"),
        (UNDERGRADUATE_FORMATION, "Undergraduate Formation"),
    ]

    STAGE_CHOICES = [
        (PLANNING_STAGE, "Planning"),
        (LAUNCHED_STAGE, "Launched"),
        (ENROLLMENT_STAGE, "Enrollment"),
        (ENROLLMENT_CLOSED_STAGE, "Enrollment Closed"),
        (FINISHED_STAGE, "Finished"),
    ]

    FORMAT_CHOICES = [
        (IN_LIVE_FORMAT, "Live"),
        (ASYNCHRONOUS_FORMAT, "Asynchronous"),
    ]

    oid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    slug = models.SlugField(
        max_length=255,
        blank=False,
        unique=True,
        verbose_name="Slug",
        help_text="Unique identifier for the offering, used in URLs.",
    )
    name = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Program Name",
        help_text="Name of the academic offering. Large Name.",
    )
    long_name = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        default=None,
        verbose_name="Long Name",
        help_text="Long name of the academic offering.",
    )
    code_name = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        default=None,
        verbose_name="Code Name",
        help_text="Code name of the academic offering. Used for internal purposes. Example: 2023-1, 2023-2, etc.",
    )
    start_date = models.DateField(
        blank=False,
        verbose_name="Start Date",
    )
    end_date = models.DateField(
        blank=False,
        verbose_name="End Date",
    )
    description = models.TextField(
        blank=True,
        verbose_name="Description",
    )
    duration = models.CharField(
        max_length=50,
        blank=True,
        verbose_name="Duration (e.g. 3 months, 6 weeks, etc.)",
    )
    frequency = models.CharField(
        max_length=50,
        blank=True,
        verbose_name="Frecuency: (e.g. Weekly, Monthly, etc.)",
    )
    hours = models.PositiveIntegerField(
        blank=True,
        verbose_name="Hours",
    )
    schedule = models.CharField(
        max_length=255,
        blank=True,
        verbose_name="Schedule. (e.g. 8:00 am - 10:00 am)",
    )

    modality = models.CharField(
        blank=False,
        max_length=20,
        choices=MODALITY_CHOICES,
        default=REMOTE_MODALITY,
        verbose_name="Modality",
    )
    type = models.CharField(
        blank=False,
        max_length=40,
        choices=TYPE_CHOICES,
        default=SPECIALIZATION_TYPE,
        verbose_name="Type",
    )
    stage = models.CharField(
        blank=False,
        max_length=20,
        choices=STAGE_CHOICES,
        default=PLANNING_STAGE,
        verbose_name="Status",
    )
    format = models.CharField(
        blank=False,
        max_length=20,
        choices=FORMAT_CHOICES,
        default=IN_LIVE_FORMAT,
        verbose_name="Format",
    )
    thumbnail = models.ForeignKey(
        "File",
        on_delete=models.SET_NULL,
        related_name="programs",
        blank=True,
        null=True,
        verbose_name="Thumbnail",
    )
    base_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=False,
        verbose_name="Base Price in PEN (S/.)",
    )
    foreign_base_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=False,
        verbose_name="Base Price in USD ($)",
    )
    discount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=False,
        verbose_name="Discount percentage",
    )
    instructors = models.ManyToManyField(
        "core.Instructor",
        through="Attachment",
        through_fields=("offering", "instructor"),
        related_name="attached_offerings",
        verbose_name="Attached Instructors",
    )
    objectives = models.JSONField(
        default=list,
        blank=True,
        verbose_name="Offering objectives",
    )
    ext_reference = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        default=None,
        verbose_name="External Reference",
        help_text="External reference for the offering. Used for integration with external systems.",
    )

    team_channel_id = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        default=None,
        verbose_name="Team Channel ID",
        help_text="Team channel ID for the offering. Used for integration with external group chats (e.g. WhatsApp, Telegram, etc.).",
    )

    @cached_property
    def price(self):
        return self.base_price - self.base_price * self.discount / 100

    @cached_property
    def foreign_price(self):
        return self.foreign_base_price - self.foreign_base_price * self.discount / 100

    def __str__(self):
        return self.name

    def clean(self):
        if self.start_date and self.end_date and self.start_date > self.end_date:
            raise ValidationError("End date must be after start date")

    @property
    def students(self):
        """Get all users enrolled in this offering through their enrollments."""
        from core.models import User

        return User.objects.filter(
            enrollments__order_item__offering=self, enrollments__is_active=True
        ).distinct()

    class Meta:
        verbose_name = "Academic Offering"
        verbose_name_plural = "Academic Offerings"
        ordering = ["created_at"]


class OfferingModule(AuditBaseModel):
    omid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    title = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Module Title",
    )
    offering = models.ForeignKey(
        "Offering",
        on_delete=models.CASCADE,
        related_name="modules",
        verbose_name="Offering",
    )

    def __str__(self):
        return f"{self.offering.name} - {self.title}"

    class Meta:
        verbose_name = "Module"
        verbose_name_plural = "Modules"
        ordering = ["created_at"]


class ModuleCourse(AuditBaseModel):
    mcid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    title = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Course Title",
    )
    module = models.ForeignKey(
        "OfferingModule",
        on_delete=models.CASCADE,
        related_name="courses",
        verbose_name="Module",
    )

    def __str__(self):
        return f"{self.module.title} - {self.title}"

    class Meta:
        verbose_name = "Course"
        verbose_name_plural = "Courses"
        ordering = ["created_at"]


class Topic(AuditBaseModel):
    tid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    title = models.CharField(
        max_length=255,
        blank=False,
        verbose_name="Topic Title",
    )
    course = models.ForeignKey(
        "ModuleCourse",
        on_delete=models.CASCADE,
        related_name="topics",
        verbose_name="Course",
    )

    def __str__(self):
        return f"{self.course.title} - {self.title}"

    class Meta:
        verbose_name = "Topic"
        verbose_name_plural = "Topics"
        ordering = ["created_at"]


class Session(AuditBaseModel):
    sid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    topic = models.ForeignKey(
        "Topic",
        on_delete=models.CASCADE,
        related_name="sessions",
        verbose_name="Topic",
    )
    title = models.CharField(
        max_length=255,
        verbose_name="Session Title",
    )
    description = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ["created_at"]


class SessionResource(AuditBaseModel):
    FILE_TYPE = "FILE"
    SURVEY_TYPE = "SURVEY"
    LINK_TYPE = "LINK"
    ASSIGNMENT_TYPE = "ASSIGNMENT"

    RESOURCE_TYPES = [
        (FILE_TYPE, "File"),
        (SURVEY_TYPE, "Survey"),
        (LINK_TYPE, "Link"),
        (ASSIGNMENT_TYPE, "Assignment"),
    ]

    rid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    session = models.ForeignKey(
        "Session",
        on_delete=models.CASCADE,
        related_name="resources",
    )
    title = models.CharField(max_length=255)
    resource_type = models.CharField(
        max_length=20,
        choices=RESOURCE_TYPES,
    )
    file = models.ForeignKey(
        "File",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="session_resources",
    )
    description = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ["created_at"]

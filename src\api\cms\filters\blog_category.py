from django_filters import rest_framework as filters
from core.models import BlogCategory
from django.db.models import Q


class CmsBlogCategoryFilter(filters.FilterSet):
    search = filters.CharFilter(method="filter_search", label="Search by name")

    parent = filters.UUIDFilter(field_name="parent__cid")

    created_at = filters.DateFromToRangeFilter()

    def filter_search(self, queryset, _, value):
        return queryset.filter(
            Q(name__icontains=value) | Q(description__icontains=value)
        )

    class Meta:
        model = BlogCategory
        fields = ["parent", "created_at"]

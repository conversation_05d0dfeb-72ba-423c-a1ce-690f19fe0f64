from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from django.conf import settings
from api.mixins import SwaggerTagMixin
from api.lms.serializers.offering import LmsTeamChannelSerializer
from api.paginations import StandardResultsPagination
from api.crm.services.evolution_api import EvolutionAPIClient, EvolutionAPIError
from services.cache.redis import CacheManager
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.permissions import IsStaffUser
from api.automations.utils.text_processing import normalize_text
from api.lms.utils.team_channel import (
    TEAM_CHANNELS_PREFIX,
    TEAM_CHANNELS_CACHE_KEY,
    fetch_team_channels,
)

import logging

logger = logging.getLogger(__name__)


class TeamChannelViewSet(
    SwaggerTagMixin,
    viewsets.ReadOnlyModelViewSet,
):
    """
    ViewSet for managing team channels (WhatsApp groups) from Evolution API.
    Only supports GET operations with pagination and search functionality.
    """

    swagger_tags = ["Team Channels"]
    serializer_class = LmsTeamChannelSerializer
    pagination_class = StandardResultsPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated, IsStaffUser]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.instance_name = settings.EVOLUTION_API_INSTANCE_NAME
        self.evo_client = EvolutionAPIClient(self.instance_name)
        # Use the same cache prefix and key as the Celery task
        self.cache_manager = CacheManager(TEAM_CHANNELS_PREFIX)
        self.cache_timeout = 60 * 15  # 15 minutes

    def _get_all_team_channels(self):
        """
        Get all team channels from cache (populated by Celery task).
        Falls back to direct API call if cache is empty.
        """
        # Try to get data from cache first (populated by Celery task)
        cached_data = self.cache_manager.get(TEAM_CHANNELS_CACHE_KEY)

        if cached_data:
            logger.info(f"Retrieved {len(cached_data)} team channels from cache")
            return cached_data

        info = self.evo_client.info.get_info()
        is_evo_available = info["data"].get("status", None) == 200

        if not is_evo_available:
            logger.error("Evolution API service is not available")
            raise EvolutionAPIError(
                "Ocurrió un error al obtener los canales disponibles"
            )
        # Fallback: if cache is empty, try direct API call
        mapped_response = fetch_team_channels(self.evo_client)

        if mapped_response is None:
            return []

        return mapped_response

    def _filter_by_search(self, queryset, search_term):
        """Filter team channels by subject (case insensitive)."""
        if not search_term:
            return queryset

        search_term = normalize_text(search_term)

        return [
            channel
            for channel in queryset
            if search_term in normalize_text(channel.get("subject", ""))
        ]

    def get_queryset(self):
        """
        Override get_queryset to return filtered team channels data.
        This method is called by the pagination system.
        """
        try:
            # Get all team channels
            team_channels = self._get_all_team_channels()

            # Only team channels that starts with CEU are returned
            team_channels_prefix = settings.CEU_TEAM_CHANNELS_PREFIX.lower()
            team_channels = [
                channel
                for channel in team_channels
                if channel.get("subject", "").lower().startswith(team_channels_prefix)
            ]

            # Apply search filter if provided
            search = self.request.query_params.get("search", None)
            if search:
                team_channels = self._filter_by_search(team_channels, search)

            return team_channels

        except EvolutionAPIError as e:
            logger.error(f"Evolution API Error: {e}")
            return []
        except Exception as e:
            logger.error(f"Error al obtener los canales disponibles: {e}")
            return []

    @swagger_auto_schema(auto_schema=None)
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

from rest_framework import serializers
from core.models import File


class LmsFileSerializer(serializers.ModelSerializer):
    url = serializers.URLField(read_only=True)
    content_type = serializers.CharField(read_only=True)

    class Meta:
        model = File
        fields = [
            "fid",
            "name",
            "url",
            "content_type",
            "description",
        ]
        read_only_fields = fields

"""
Retry and recovery tasks for failed invitations
"""

import logging
from celery import shared_task
from core.models import EventReminder
from .whatsapp import schedule_whatsapp_invitations
from .email import send_email_invitation, send_bulk_email_invitations

logger = logging.getLogger(__name__)


@shared_task
def retry_failed_invitations(
    event_schedule_id: str = None, invitation_type: str = None
):
    """
    Retry failed invitations for a specific event schedule or all failed invitations
    Uses bulk processing for emails and random delays for WhatsApp to prevent Meta bans

    Args:
        event_schedule_id: Optional EventSchedule UUID to filter by
        invitation_type: Optional type ('whatsapp' or 'email') to filter by
    """
    filters = {}

    if event_schedule_id:
        filters["enrollment__event_schedule__esid"] = event_schedule_id

    retry_count = 0

    # Retry WhatsApp invitations
    if not invitation_type or invitation_type == "whatsapp":
        retry_count += _retry_whatsapp_invitations(filters)

    # Retry email invitations
    if not invitation_type or invitation_type == "email":
        retry_count += _retry_email_invitations(filters)

    logger.info(f"Scheduled {retry_count} failed invitation retries")
    return f"Scheduled {retry_count} retries"


def _retry_whatsapp_invitations(filters: dict) -> int:
    """
    Retry failed WhatsApp invitations by resetting them to PENDING status
    and letting the unified scheduling system handle them properly

    Args:
        filters: Base filters to apply

    Returns:
        int: Number of retries scheduled
    """
    whatsapp_filters = {
        **filters,
        "status_whatsapp": EventReminder.FAILED,
        "retry_count_whatsapp__lt": 3,
        "enrollment__event_schedule__whatsapp_template__isnull": False,
        "enrollment__event_schedule__is_whatsapp_active": True,
    }

    failed_whatsapp = EventReminder.objects.filter(**whatsapp_filters).select_related(
        "enrollment__event_schedule"
    )

    if not failed_whatsapp.exists():
        logger.info("No failed WhatsApp invitations found for retry")
        return 0

    # Reset all failed reminders to PENDING status
    # The unified scheduling system will handle them with proper delays
    retry_count = 0
    for reminder in failed_whatsapp:
        reminder.status_whatsapp = EventReminder.PENDING
        reminder.save(update_fields=["status_whatsapp"])
        retry_count += 1

        logger.info(
            f"Reset WhatsApp reminder {reminder.rid} to PENDING for retry "
            f"(attempt {reminder.retry_count_whatsapp + 1}/3)"
        )

    # Use the unified scheduling system to process the reset reminders
    # This ensures proper delay handling and prevents Meta bans
    # scheduled_count = schedule_whatsapp_invitations(failed_whatsapp)

    logger.info(f"Reset {retry_count} failed WhatsApp reminders to PENDING, ")

    return retry_count


def _retry_email_invitations(filters: dict) -> int:
    """
    Retry failed email invitations using bulk processing when appropriate

    Args:
        filters: Base filters to apply

    Returns:
        int: Number of retries scheduled
    """
    email_filters = {
        **filters,
        "status_email": EventReminder.FAILED,
        "retry_count_email__lt": 3,
    }

    failed_email = EventReminder.objects.filter(**email_filters).select_related(
        "enrollment__event_schedule"
    )

    # Group by event schedule for bulk processing
    event_schedules_with_failed_emails = {}

    for reminder in failed_email:
        # Reset status before retry
        reminder.status_email = EventReminder.PENDING
        reminder.save(update_fields=["status_email"])

        event_schedule_id = str(reminder.enrollment.event_schedule.esid)
        if event_schedule_id not in event_schedules_with_failed_emails:
            event_schedules_with_failed_emails[event_schedule_id] = []
        event_schedules_with_failed_emails[event_schedule_id].append(reminder)

    retry_count = 0

    # Process retries with bulk when multiple failures for same event
    for event_schedule_id, reminders in event_schedules_with_failed_emails.items():
        if len(reminders) > 1:
            # Use bulk processing for multiple failed reminders
            enrollment_ids = [r.enrollment.id for r in reminders]
            send_bulk_email_invitations.delay(event_schedule_id, enrollment_ids)
            retry_count += len(reminders)
            logger.info(
                f"Scheduled bulk email retry for event schedule {event_schedule_id} "
                f"({len(reminders)} reminders)"
            )
        else:
            # Use individual processing for single failures
            send_email_invitation.delay(str(reminders[0].rid))
            retry_count += 1
            logger.info(
                f"Scheduled individual email retry for reminder {reminders[0].rid}"
            )

    return retry_count

from rest_framework import permissions


class IsOwnerOrStaff(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object or staff users to access it.
    """

    def has_object_permission(self, request, view, obj):
        # Staff users can access anything
        if request.user.is_staff:
            return True

        # Check if the object has a user attribute and if it matches the request user
        return hasattr(obj, "user") and obj.user == request.user

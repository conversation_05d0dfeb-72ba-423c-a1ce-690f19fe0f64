# For common environment variables across services.
x-common-variables: &common-variables
  CELERY_BROKER_URL: ${CELERY_BROKER_URL}
  CELERY_RESULT_BACKEND: ${CELERY_RESULT_BACKEND}
  EMAIL_HOST: ${EMAIL_HOST}
  EMAIL_PORT: ${EMAIL_PORT}
  EMAIL_USE_TLS: ${EMAIL_USE_TLS}
  EMAIL_HOST_USER: ${EMAIL_HOST_USER}
  EMAIL_HOST_PASSWORD: ${EMAIL_HOST_PASSWORD}
  DEFAULT_FROM_EMAIL: ${DEFAULT_FROM_EMAIL}
  TOKECHAT_API_KEY: ${TOKECHAT_API_KEY}
  POSTGRES_DB: ${POSTGRES_DB}
  POSTGRES_USER: ${POSTGRES_USER}
  POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
  POSTGRES_HOST: ${POSTGRES_HOST}
  POSTGRES_PORT: ${POSTGRES_PORT}
  REDIS_HOST: ${REDIS_HOST}
  REDIS_PORT: ${REDIS_PORT}
  MINIO_HOST: ${MINIO_HOST}
  MINIO_PORT: ${MINIO_PORT:-9000}
  DEBUG: ${DEBUG}
  STAGE: ${STAGE:-PRODUCTION}
  SECRET_KEY: ${SECRET_KEY}
  ALLOWED_HOSTS: ${ALLOWED_HOSTS}
  CORS_ALLOWED_ORIGINS: ${CORS_ALLOWED_ORIGINS}
  MINIO_ENDPOINT: ${MINIO_ENDPOINT}
  MINIO_PUBLIC_ENDPOINT: ${MINIO_PUBLIC_ENDPOINT}
  MINIO_ACCESS_KEY: ${MINIO_ACCESS_KEY}
  MINIO_SECRET_KEY: ${MINIO_SECRET_KEY}
  MINIO_SECURE: ${MINIO_SECURE}
  MINIO_REGION: ${MINIO_REGION}
  MINIO_PRIVATE_SECURE: ${MINIO_PRIVATE_SECURE}
  PYTHONPATH: /home/<USER>/app
  DJANGO_STATIC_ROOT: /home/<USER>/app/staticfiles
  DJANGO_SUPERUSER_USERNAME: ${DJANGO_SUPERUSER_USERNAME}
  DJANGO_SUPERUSER_EMAIL: ${DJANGO_SUPERUSER_EMAIL}
  DJANGO_SUPERUSER_PASSWORD: ${DJANGO_SUPERUSER_PASSWORD}
  CSRF_TRUSTED_ORIGINS: ${CSRF_TRUSTED_ORIGINS}
  APP_HOST: ${APP_HOST}
  MP_ACCESS_TOKEN: ${MP_ACCESS_TOKEN}
  MP_PUBLIC_KEY: ${MP_PUBLIC_KEY}
  PAYPAL_BASE_URL: ${PAYPAL_BASE_URL}
  PAYPAL_CLIENT_ID: ${PAYPAL_CLIENT_ID}
  PAYPAL_CLIENT_SECRET: ${PAYPAL_CLIENT_SECRET}
  EXCHANGE_RATE_API_KEY: ${EXCHANGE_RATE_API_KEY}
  MONTHLY_SALES_TARGET: ${MONTHLY_SALES_TARGET}
  CEU_TEAM_CHANNELS_PREFIX: ${CEU_TEAM_CHANNELS_PREFIX}
  REDIS_DB: ${REDIS_DB}
  REDIS_CACHE_TIMEOUT: ${REDIS_CACHE_TIMEOUT}
  REDIS_MAX_CONNECTIONS: ${REDIS_MAX_CONNECTIONS}
  EVOLUTION_API_ACCESS_TOKEN: ${EVOLUTION_API_ACCESS_TOKEN}
  EVOLUTION_API_HOST: ${EVOLUTION_API_HOST}
  EVOLUTION_API_INSTANCE_NAME: ${EVOLUTION_API_INSTANCE_NAME}
  TURNSTILE_SECRET_KEY: ${TURNSTILE_SECRET_KEY}

# Base configuration for the API services, now pulling from ECR.
x-api-common: &api-common
  # The image is pulled from ECR. The URL is built from .env variables.
  image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/portals-api:${IMAGE_TAG:-latest}
  restart: unless-stopped
  environment:
    <<: *common-variables
  volumes:
    - "${SECRETS_HOST_PATH:-./secrets}:/home/<USER>/app/secrets:ro"
  networks:
    - ceu-network

# Base configuration for Celery services, now pulling from ECR.
x-celery-common: &celery-common
  # The image is pulled from ECR.
  image: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/portals-celery:${IMAGE_TAG:-latest}
  restart: unless-stopped
  environment:
    <<: *common-variables
  depends_on:
    - api
  networks:
    - ceu-network
  volumes:
    - "${SECRETS_HOST_PATH:-./secrets}:/app/secrets:ro"
  healthcheck:
    test: ["CMD", "celery", "-A", "core", "inspect", "ping"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 40s

# --- Services ---
services:
  api:
    <<: *api-common
    container_name: portals-api
    command: ["/entrypoint.sh"]
    ports:
      - "${API_HOST_PORT:-8004}:8000"

  celery-worker-1:
    <<: *celery-common
    container_name: portals-celery-worker-1
    command: celery -A core worker --loglevel=${CELERY_LOG_LEVEL:-info} --hostname=worker1@%h

  celery-worker-2:
    <<: *celery-common
    container_name: portals-celery-worker-2
    command: celery -A core worker --loglevel=${CELERY_LOG_LEVEL:-info} --hostname=worker2@%h

  celery-beat:
    <<: *celery-common
    container_name: portals-celery-beat
    command: celery -A core beat --loglevel=${CELERY_LOG_LEVEL:-info}

# --- Networks ---
networks:
  ceu-network:
    external: true

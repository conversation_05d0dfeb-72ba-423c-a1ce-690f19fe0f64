# Generated by Django 5.0.6 on 2025-08-30 17:55

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0022_eventscheduleenrollment_ceu_to_apply"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserPreference",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "allow_event_invites_whatsapp",
                    models.BooleanField(
                        default=True,
                        help_text="Permite recibir invitaciones a eventos a través de WhatsApp.",
                    ),
                ),
                (
                    "notify_by_email",
                    models.BooleanField(
                        default=True,
                        help_text="Recibir notificaciones generales por correo electrónico.",
                    ),
                ),
                (
                    "notify_by_push",
                    models.BooleanField(
                        default=True,
                        help_text="Recibir notificaciones a través de notificaciones push en la app o navegador.",
                    ),
                ),
                (
                    "notify_security_alerts",
                    models.BooleanField(
                        default=True,
                        help_text="Recibir alertas de seguridad importantes, como intentos de inicio de sesión sospechosos.",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        help_text="Usuario al que están asociadas estas preferencias.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="preferences",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]

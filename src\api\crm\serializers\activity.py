from rest_framework import serializers
from django.utils import timezone
from core.models import Activity, Order, User


class CrmActivityResponsibleSerializer(serializers.ModelSerializer):
    """Serializer for the responsible user of an activity"""

    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "uid",
            "full_name",
            "email",
            "phone_number",
        ]

    def get_full_name(self, obj):
        return obj.get_full_name() if obj.get_full_name() != "" else "No name"


class CrmActivityOrderSerializer(serializers.ModelSerializer):
    """Serializer for the order associated with an activity"""

    owner = serializers.SerializerMethodField()

    def get_owner(self, obj):
        """Get the owner of the order"""
        return obj.owner.get_full_name() if obj.owner else "No owner"

    class Meta:
        model = Order
        fields = [
            "oid",
            "stage",
            "owner",
        ]

        extra_kwargs = {
            "oid": {"read_only": True},
            "stage": {"read_only": True},
        }


class CrmActivityBaseSerializer(serializers.ModelSerializer):
    """Base serializer with common fields for all operations"""

    key = serializers.CharField(source="aid", read_only=True)

    class Meta:
        model = Activity
        fields = [
            "key",
            "aid",
            "title",
            "description",
            "deadline",
            "responsible",
            "status",
            "order",
            "created_at",
            "updated_at",
        ]

        extra_kwargs = {
            "aid": {"read_only": True},
            "created_at": {"read_only": True},
            "updated_at": {"read_only": True},
        }


class CrmActivityListItemSerializer(CrmActivityBaseSerializer):
    """Serializer for listing activities"""

    responsible = CrmActivityResponsibleSerializer(read_only=True)
    order = CrmActivityOrderSerializer(read_only=True)

    class Meta(CrmActivityBaseSerializer.Meta):
        fields = CrmActivityBaseSerializer.Meta.fields


class CrmActivityRetrieveSerializer(CrmActivityBaseSerializer):
    """Serializer for retrieving activity details with nested relationships"""

    responsible = CrmActivityResponsibleSerializer(read_only=True)
    order = CrmActivityOrderSerializer(read_only=True)

    class Meta(CrmActivityBaseSerializer.Meta):
        fields = CrmActivityBaseSerializer.Meta.fields


class CrmActivityCreateSerializer(CrmActivityBaseSerializer):
    """Serializer for creating new activities"""

    title = serializers.CharField(max_length=255, required=True)
    description = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )
    deadline = serializers.DateTimeField(required=False, allow_null=True)
    responsible = serializers.UUIDField(required=False, allow_null=True)
    status = serializers.ChoiceField(
        choices=Activity.STATUS_CHOICES, default=Activity.PENDING_STATUS, required=False
    )
    order = serializers.UUIDField(required=False, allow_null=True)

    class Meta(CrmActivityBaseSerializer.Meta):
        fields = [
            "title",
            "description",
            "deadline",
            "responsible",
            "status",
            "order",
        ]

    def validate_title(self, value):
        """Validate that title is not empty"""
        if not value or value.strip() == "":
            raise serializers.ValidationError("Title cannot be empty")
        return value.strip()

    def validate_responsible(self, value):
        """Validate that the responsible user exists"""
        if value:
            try:
                user = User.objects.get(uid=value, deleted=False)
                return user
            except User.DoesNotExist:
                raise serializers.ValidationError(
                    f"User with ID {value} does not exist"
                )
        return None

    def validate_order(self, value):
        """Validate that the order exists"""
        if value:
            try:
                order = Order.objects.get(oid=value, deleted=False)
                return order
            except Order.DoesNotExist:
                raise serializers.ValidationError(
                    f"Order with ID {value} does not exist"
                )
        return None

    def validate_deadline(self, value):
        """Validate that deadline is not in the past"""
        if value and value < timezone.now():
            raise serializers.ValidationError("Deadline cannot be in the past")
        return value

    def validate(self, attrs):
        """
        Validate business rules between fields
        """
        status = attrs.get("status")
        deadline = attrs.get("deadline")

        # If status is completed, we might want to set specific validations
        if status == Activity.COMPLETED_STATUS and deadline:
            if deadline > timezone.now():
                # Warning: completing a task before its deadline is fine
                pass

        return attrs

    def create(self, validated_data):
        return super().create(validated_data)


class CrmActivityUpdateSerializer(CrmActivityBaseSerializer):
    """Serializer for updating existing activities"""

    title = serializers.CharField(max_length=255, required=False)
    description = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )
    deadline = serializers.DateTimeField(required=False, allow_null=True)
    responsible = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.filter(deleted=False), required=False, allow_null=True
    )
    status = serializers.ChoiceField(choices=Activity.STATUS_CHOICES, required=False)
    order = serializers.PrimaryKeyRelatedField(
        queryset=Order.objects.filter(deleted=False), required=False, allow_null=True
    )

    class Meta(CrmActivityBaseSerializer.Meta):
        fields = CrmActivityBaseSerializer.Meta.fields

    def validate_title(self, value):
        """Validate that title is not empty if provided"""
        if value is not None and (not value or value.strip() == ""):
            raise serializers.ValidationError("Title cannot be empty")
        return value.strip() if value else value

    def validate_deadline(self, value):
        """Validate that deadline is not in the past if provided"""
        if value and value < timezone.now():
            # Allow past deadlines for updates (user might be updating an overdue task)
            pass
        return value

    def update(self, instance, validated_data):
        """
        Custom update logic to handle status changes
        """
        # If status is being changed to completed, we might want to add some logic
        old_status = instance.status
        new_status = validated_data.get("status", old_status)

        if old_status != new_status and new_status == Activity.COMPLETED_STATUS:
            # Task is being marked as completed
            # Add any completion logic here if needed
            pass

        return super().update(instance, validated_data)

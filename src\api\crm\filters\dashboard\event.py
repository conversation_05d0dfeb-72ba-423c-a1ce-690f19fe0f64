"""
Event Dashboard Filters for CRM
Provides filtering capabilities for event dashboard analytics
"""

from django_filters import rest_framework as filters
from core.models import EventSchedule, Event, Offering
from django.db.models import Q
from django.utils import timezone
from datetime import datetime, time


class CrmDashboardEventFilter(filters.FilterSet):
    """
    Filter for Event Dashboard analytics
    Supports filtering by date ranges, programs (offerings), and specific events
    """

    # Program filter (offerings) - multiple selection
    programs = filters.CharFilter(
        method="filter_by_programs",
        help_text="Filter by programs/offerings (comma-separated list of offering IDs)",
    )

    # Specific event filter
    events = filters.CharFilter(
        method="filter_by_event",
        help_text="Filter by events (comma-separated list of event IDs)",
    )

    # Event stage filter
    stage = filters.CharFilter(
        method="filter_by_stage",
        help_text="Filter by event stage (comma-separated list)",
    )

    # Event type filter
    event_type = filters.CharFilter(
        method="filter_by_event_type",
        help_text="Filter by event type (comma-separated list)",
    )

    # Event modality filter
    modality = filters.CharFilter(
        method="filter_by_modality",
        help_text="Filter by event modality (comma-separated list)",
    )

    # Date range filters
    start_date = filters.DateFilter(
        method="filter_by_start_date",
        help_text="Filter events starting from this date (YYYY-MM-DD)",
    )

    end_date = filters.DateFilter(
        method="filter_by_end_date",
        help_text="Filter events ending before this date (YYYY-MM-DD)",
    )

    # Filter by partnerships (comma-separated list of partnership IDs)
    partnerships = filters.CharFilter(
        method="filter_by_partnerships",
        help_text="Filter by partnerships (comma-separated list of partnership IDs)",
    )

    force_refresh = filters.BooleanFilter(
        method="filter_force_refresh", help_text="Force refresh cache (true/false)"
    )

    class Meta:
        model = EventSchedule
        fields = [
            "start_date",
            "end_date",
            "programs",
            "events",
            "stage",
            "event_type",
            "modality",
            "partnerships",
        ]

    def filter_force_refresh(self, queryset, name, value):
        """
        Dummy filter for force_refresh - actual logic is in ViewSet.initial()
        """
        return queryset

    def filter_by_programs(self, queryset, name, value):
        """
        Filter by programs/offerings (comma-separated list of offering IDs)
        Example: ?programs=uuid1,uuid2,uuid3
        """
        if not value:
            return queryset

        program_ids = [pid.strip() for pid in value.split(",")]
        return queryset.filter(event__offering__oid__in=program_ids).distinct()

    def filter_by_event(self, queryset, name, value):
        """
        Filter by events (comma-separated list of event IDs)
        Example: ?event=uuid1,uuid2,uuid3
        """
        if not value:
            return queryset

        event_ids = [eid.strip() for eid in value.split(",")]
        return queryset.filter(event__eid__in=event_ids).distinct()

    def filter_by_stage(self, queryset, name, value):
        """
        Filter by event stage (comma-separated list)
        Example: ?stage=planning,launched
        """
        if not value:
            return queryset

        stages = [stage.strip() for stage in value.split(",")]
        return queryset.filter(stage__in=stages)

    def filter_by_event_type(self, queryset, name, value):
        """
        Filter by event type (comma-separated list)
        Example: ?event_type=workshop,webinar
        """
        if not value:
            return queryset

        types = [event_type.strip() for event_type in value.split(",")]
        return queryset.filter(event__type__in=types)

    def filter_by_modality(self, queryset, name, value):
        """
        Filter by event modality (comma-separated list)
        Example: ?modality=remote,in_person
        """
        if not value:
            return queryset

        modalities = [modality.strip() for modality in value.split(",")]
        return queryset.filter(modality__in=modalities)

    def filter_by_start_date(self, queryset, name, value):
        """
        Filter events starting from the given date (inclusive)
        Converts date to datetime at start of day (00:00:00)
        Example: ?start_date=2025-09-01
        """
        if not value:
            return queryset

        # Convert date to datetime at start of day in local timezone
        local_tz = timezone.get_current_timezone()
        start_datetime = timezone.make_aware(
            datetime.combine(value, time.min), local_tz
        )

        return queryset.filter(start_date__gte=start_datetime)

    def filter_by_end_date(self, queryset, name, value):
        """
        Filter events ending before the given date (inclusive)
        Converts date to datetime at end of day (23:59:59.999999)
        Example: ?end_date=2025-09-07
        """
        if not value:
            return queryset

        # Convert date to datetime at end of day in local timezone
        local_tz = timezone.get_current_timezone()
        end_datetime = timezone.make_aware(datetime.combine(value, time.max), local_tz)

        return queryset.filter(start_date__lte=end_datetime)

    def filter_by_partnerships(self, queryset, name, value):
        """
        Filter by partnerships (comma-separated list of partnership IDs)
        Example: ?partnerships=uuid1,uuid2,uuid3
        """
        if not value:
            return queryset

        partnerships = [partnership.strip() for partnership in value.split(",")]
        return queryset.filter(
            partnerships__in=partnerships,
        ).distinct()

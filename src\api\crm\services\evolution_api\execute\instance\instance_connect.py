"""
Connect WhatsApp instance via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def instance_connect(instance_name: str) -> Dict[str, Any]:
    """
    Connect a WhatsApp instance

    Args:
        instance_name: Name of the instance to connect

    Returns:
        Dict containing the API response with connection data

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Make the request
        response = evolution_request(
            uri=f"/instance/connect/{instance_name}", method="GET"
        )

        logger.info(f"Instance '{instance_name}' connection initiated successfully")
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to connect instance '{instance_name}': {e.message}")
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error connecting instance '{instance_name}': {str(e)}"
        )
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")

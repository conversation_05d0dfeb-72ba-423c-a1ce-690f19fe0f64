from celery import shared_task
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from core.models import StudentEnrollment
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def send_certificate_email(self, enrollment_id: str):
    logger.info(f"Starting certificate email task for enrollment {enrollment_id}")

    # Get the enrollment and certificate details
    try:
        self.update_state(
            state="PROGRESS",
            meta={"message": "Consultando información de matrícula..."},
        )
        logger.info(f"Fetching enrollment data for {enrollment_id}")
        enrollment = StudentEnrollment.objects.get(eid=enrollment_id)
        certificate = enrollment.certificate

        if not certificate or not certificate.file:
            logger.error(f"No certificate found for enrollment {enrollment_id}")
            return

        logger.info(
            f"Certificate found for enrollment {enrollment_id}, "
            f"certificate ID: {certificate.cid}"
        )

        certificate_url = f"{settings.APP_HOST}/credencial/{certificate.cid}"
        logger.info(f"Certificate URL generated: {certificate_url}")

        subject = "Tu certificado de finalización ya está disponible!"
        from_email = settings.DEFAULT_FROM_EMAIL
        to = [enrollment.user.email]

        logger.info(f"Preparing email for {enrollment.user.email}")

        self.update_state(
            state="PROGRESS",
            meta={"message": "Generando el contenido del correo..."},
        )

        logger.info("Rendering email template")
        student_name = f"{enrollment.user.first_name} {enrollment.user.last_name}"
        offering_name = (
            enrollment.order_item.offering.long_name
            if enrollment.order_item.offering.long_name
            else enrollment.order_item.offering.name
        )

        html_content = render_to_string(
            "emails/send_certificate.html",
            context={
                "student_name": student_name,
                "certificate_url": certificate_url,
                "offering_name": offering_name,
            },
        )

        logger.info(f"Email template rendered for student: {student_name}")

        text_content = strip_tags(html_content)

        logger.info("Creating email message")
        msg = EmailMultiAlternatives(subject, text_content, from_email, to)
        msg.attach_alternative(html_content, "text/html")

        self.update_state(
            state="PROGRESS",
            meta={"message": "Enviando el correo..."},
        )

        logger.info(f"Sending certificate email to {enrollment.user.email}")
        msg.send()

        enrollment.certificate_sent = True
        enrollment.save(update_fields=["certificate_sent"])

        logger.info(
            f"Certificate email successfully sent for enrollment {enrollment_id} "
            f"to {enrollment.user.email}"
        )

        self.update_state(
            state="SUCCESS",
            meta={"message": "Correo enviado exitosamente."},
        )

    except StudentEnrollment.DoesNotExist:
        logger.error(f"StudentEnrollment with ID {enrollment_id} not found")
        # self.retry(countdown=60)
    except Exception as e:
        logger.error(
            f"Error sending certificate email for enrollment {enrollment_id}: {str(e)}"
        )
        # self.retry(countdown=60)

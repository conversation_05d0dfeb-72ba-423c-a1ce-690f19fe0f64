from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Q
from core.models.template import Template
from api.crm.serializers.template import (
    CrmCreateTemplateSerializer,
    CrmTemplateSerializer,
    CrmUpdateTemplateSerializer,
    CrmSendTemplateSerializer,
    CrmFetchTemplateStatusSerializer,
    CrmRetrieveTemplateSerializer,
    CrmSendPreviewTemplateSerializer,
    CrmBulkDeleteTemplateSerializer,
)
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import (
    IsAuthenticated,
    IsAdminUser,
    DjangoModelPermissions,
)
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.filters.template import CrmTemplateFilter
from api.crm.tasks import sync_whatsapp_templates
from api.crm.services.invitations.whatsapp import TokeChatAPIService
from services.cache.redis import CacheManager
from api.paginations import StandardResultsPagination
from api.crm.serializers.template import CrmSendTemplateSerializer
from api.crm.services.evolution_api import EvolutionAPIService


class CrmTemplateViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Template
    queryset = Template.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = CrmTemplateSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser & DjangoModelPermissions]
    filterset_class = CrmTemplateFilter
    pagination_class = StandardResultsPagination
    swagger_tags = ["Templates"]

    # rate limiting for syncing templates
    cache_manager = CacheManager(prefix="templates", timeout=120)  # 2 minutes

    def get_serializer_class(self):
        if self.action == "create":
            return CrmCreateTemplateSerializer
        if self.action == "retrieve":
            return CrmRetrieveTemplateSerializer
        if self.action in ["update", "partial_update"]:
            return CrmUpdateTemplateSerializer

        return super().get_serializer_class()

    @action(detail=True, methods=["get"])
    def refresh_meta_status(self, request, pk=None):
        # Manual refresh endpoint
        instance = self.get_object()
        try:
            new_status = CrmFetchTemplateStatusSerializer().fetch_meta_template_status(
                instance
            )
            return Response({"status": new_status}, status=status.HTTP_200_OK)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=["post"], url_path="send-to-meta")
    def send_to_meta(self, request, pk=None):
        """
        Acción personalizada para enviar una plantilla a Meta.
        """
        template = self.get_object()
        serializer = CrmSendTemplateSerializer(template)

        try:
            serializer.send_to_meta(template)
            return Response(
                {"detail": "Plantilla enviada a Meta con éxito."},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"detail": f"{str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @action(
        detail=False, methods=["post"], url_path="sync-templates", serializer_class=None
    )
    def sync_templates(self, request, pk=None):
        """
        Sincroniza las plantillas con la base de datos (síncrono).
        TODO: Modificar sincronización para verificar el estado de aprobación de plantillas si se requiere
        usar Meta API
        """
        # Usar cache manager como rate limiting para evitar múltiples ejecuciones simultáneas
        cache_key = "sync_templates_in_progress"
        info = self.cache_manager.debug_cache_info(cache_key)
        ttl_seconds = info.get("ttl_seconds", None)

        if self.cache_manager.get(cache_key):
            return Response(
                {
                    "detail": f" Inténtalo de nuevo en {ttl_seconds} segundos.",
                },
                status=status.HTTP_429_TOO_MANY_REQUESTS,
            )

        tokechat = TokeChatAPIService()

        if not tokechat.is_available():
            return Response(
                {"detail": "TokeChat API no disponible"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        result = sync_whatsapp_templates()

        # si result es un error msg, retornar error, si empieza con Error syncing
        if result.startswith("Error"):
            return Response(
                {"detail": "Error al sincronizar las plantillas"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        # set cache to True
        self.cache_manager.set(cache_key, True)

        return Response(
            {"detail": "Plantillas sincronizadas"},
            status=status.HTTP_200_OK,
        )

    # Send test message with template to WhatsApp using the message api service
    @action(
        detail=True,
        methods=["post"],
        url_path="send-test-message",
        serializer_class=CrmSendPreviewTemplateSerializer,
    )
    def send_test_message(self, request, pk=None):
        """
        Send a test message with the template to WhatsApp using the message api service
        """
        whatsapp_service: EvolutionAPIService = EvolutionAPIService()
        template = self.get_object()

        try:
            whatsapp_service.send_event_invitation_preview(
                phone_number=request.data.get("phone_number"),
                template=template,
            )

            return Response(
                {"detail": "Plantilla enviada a prueba."},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"detail": f"{str(e)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def destroy(self, request, *args, **kwargs):
        """
        Marca una plantilla como eliminada en lugar de eliminarla físicamente.
        """
        instance = self.get_object()
        instance.deleted = True
        instance.save()

        return Response(
            {"detail": "La plantilla se marcó como eliminada."},
            status=status.HTTP_204_NO_CONTENT,
        )

    @action(
        detail=False,
        methods=["post"],
        url_path="bulk-delete",
        serializer_class=CrmBulkDeleteTemplateSerializer,
    )
    def bulk_delete(self, request):
        """
        Realiza una eliminación lógica en masa de registros basados en un array de IDs.
        """
        serializer = CrmBulkDeleteTemplateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        tids = serializer.validated_data.get("tids", [])
        if not tids or not isinstance(tids, list):
            return Response(
                {"detail": "Se debe proporcionar una lista de IDs."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Filtrar las plantillas que coincidan con los IDs proporcionados
        templates = self.queryset.filter(Q(tid__in=tids), deleted=False)

        if not templates.exists():
            return Response(
                {"detail": "No se encontraron plantillas válidas para eliminar."},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Realizar la eliminación lógica
        templates.update(deleted=True)

        return Response(
            {"detail": f"Se marcaron como eliminadas {templates.count()} plantillas."},
            status=status.HTTP_200_OK,
        )

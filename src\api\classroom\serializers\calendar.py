from rest_framework import serializers
from core.models.event import EventSchedule


class EventScheduleSerializer(serializers.ModelSerializer):

    event_name = serializers.CharField(source="event.name", read_only=True)
    type = serializers.CharField(source="event.type", read_only=True)

    class Meta:
        model = EventSchedule
        fields = [
            "esid",
            "short_esid",
            "event_name",
            "name",
            "description",
            "start_date",
            "end_date",
            "modality",
            "location",
            "type",
        ]
        read_only_fields = fields

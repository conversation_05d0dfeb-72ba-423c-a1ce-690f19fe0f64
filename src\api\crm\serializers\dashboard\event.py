# Event Dashboard Serializers for CRM

from rest_framework import serializers


class CrmDashboardEventStatsSerializer(serializers.Serializer):
    """Serializer for event stage statistics"""

    planning = serializers.IntegerField()
    launched = serializers.IntegerField()
    enrollment_closed = serializers.IntegerField()
    finished = serializers.IntegerField()
    total = serializers.IntegerField()


class CrmDashboardEventConversionRateSerializer(serializers.Serializer):
    """Serializer for conversion rate statistics"""

    percentage = serializers.DecimalField(max_digits=5, decimal_places=2)
    converted = serializers.IntegerField()
    total_enrollments = serializers.IntegerField()


class CrmDashboardEventEnrollmentSegmentationSerializer(serializers.Serializer):
    """Serializer for enrollment segmentation by organic/paid status"""

    organic = serializers.IntegerField()
    paid = serializers.IntegerField()
    total = serializers.IntegerField()


class CrmDashboardEventSummarySerializer(serializers.Serializer):
    """Serializer for event dashboard summary endpoint"""

    stats = CrmDashboardEventStatsSerializer()
    needs_conciliation = serializers.IntegerField()
    alliances_enrollments = serializers.IntegerField()
    conversion = CrmDashboardEventConversionRateSerializer()
    enrollments = CrmDashboardEventEnrollmentSegmentationSerializer()
    filters_applied = serializers.DictField(read_only=True)


class CrmDashboardEventByTypeSerializer(serializers.Serializer):
    """Serializer for event type statistics"""

    count = serializers.IntegerField()
    total_enrollments = serializers.IntegerField()


class CrmDashboardEventTypeStatsSerializer(serializers.Serializer):
    """Serializer for event by type statistics"""

    workshop = CrmDashboardEventByTypeSerializer()
    webinar = CrmDashboardEventByTypeSerializer()
    hands_on_workshop = CrmDashboardEventByTypeSerializer()


class CrmDashboardEventDiffusionChannelSerializer(serializers.Serializer):
    """Serializer for diffusion channel statistics"""

    channel = serializers.CharField()
    total = serializers.IntegerField()
    has_contact = serializers.IntegerField()
    needs_conciliation = serializers.IntegerField()
    already_lead = serializers.IntegerField()


class CrmDashboardEventTopAllianceSerializer(serializers.Serializer):
    """Serializer for top alliance statistics"""

    alliance_name = serializers.CharField()
    associated_events_count = serializers.IntegerField()
    unique_enrollments = serializers.IntegerField()
    total_enrollments = serializers.IntegerField()
    global_participation_percentage = serializers.DecimalField(
        max_digits=5, decimal_places=2
    )
    participation_percentage = serializers.DecimalField(max_digits=5, decimal_places=2)


class CrmDashboardEventAnalyticsSerializer(serializers.Serializer):
    """Serializer for event dashboard analytics endpoint"""

    event_by_type = CrmDashboardEventTypeStatsSerializer()
    diffusion_channels = CrmDashboardEventDiffusionChannelSerializer(many=True)
    top_alliances = CrmDashboardEventTopAllianceSerializer(many=True)
    filters_applied = serializers.DictField(read_only=True)


class CrmDashboardEventInterestBreakdownSerializer(serializers.Serializer):
    """Serializer for interest breakdown statistics"""

    specialization = serializers.CharField()
    count = serializers.IntegerField()


class CrmDashboardEventCeuToApplyBreakdownSerializer(serializers.Serializer):
    """Serializer for CEU to apply breakdown statistics"""

    ceu_to_apply = serializers.CharField()
    count = serializers.IntegerField()


class CrmDashboardEventContactSegmentSerializer(serializers.Serializer):
    """Serializer for contact segment statistics"""

    total = serializers.IntegerField()
    percentage = serializers.DecimalField(max_digits=5, decimal_places=2)


class CrmDashboardEventContactsSegmentationSerializer(serializers.Serializer):
    """Serializer for contacts segmentation statistics"""

    new_contacts = CrmDashboardEventContactSegmentSerializer()
    has_contact = CrmDashboardEventContactSegmentSerializer()


class CrmDashboardEventSegmentationSerializer(serializers.Serializer):
    """Serializer for event dashboard segmentation endpoint"""

    interests = CrmDashboardEventInterestBreakdownSerializer(many=True)
    ceu_to_apply = CrmDashboardEventCeuToApplyBreakdownSerializer(many=True)
    contacts = CrmDashboardEventContactsSegmentationSerializer()
    filters_applied = serializers.DictField(read_only=True)


class CrmDashboardEventInvitationStatusDetailSerializer(serializers.Serializer):
    """Serializer for invitation status details"""

    total_sent = serializers.IntegerField()
    total_failed = serializers.IntegerField()
    total_pending = serializers.IntegerField()


class CrmDashboardEventInvitationStatusSerializer(serializers.Serializer):
    """Serializer for invitation status statistics"""

    total_reminders = serializers.IntegerField()
    total_sent = serializers.IntegerField()
    total_pending = serializers.IntegerField()
    total_failed = serializers.IntegerField()
    email = CrmDashboardEventInvitationStatusDetailSerializer()
    whatsapp = CrmDashboardEventInvitationStatusDetailSerializer()


class CrmDashboardEventLaunchedEventSerializer(serializers.Serializer):
    """Serializer for launched event details"""

    IN_COURSE = "in_course"
    FUTURE = "future"
    PAST = "past"

    EVENT_STATUS_CHOICES = [
        (IN_COURSE, "In Course"),
        (FUTURE, "Future"),
        (PAST, "Past"),
    ]

    esid = serializers.UUIDField()
    event_name = serializers.CharField()
    start_date = serializers.DateTimeField()
    end_date = serializers.DateTimeField()
    ext_event_link = serializers.URLField(allow_null=True)
    enrollment_count = serializers.IntegerField()
    invitation_status = CrmDashboardEventInvitationStatusSerializer()
    event_status = serializers.ChoiceField(choices=EVENT_STATUS_CHOICES)
    offering = serializers.CharField()


class CrmDashboardEventLaunchedSerializer(serializers.Serializer):
    """Serializer for event dashboard launched endpoint"""

    def to_representation(self, instance):
        """Custom representation for list of launched events"""
        if isinstance(instance, list):
            return [
                CrmDashboardEventLaunchedEventSerializer(item).data for item in instance
            ]
        return CrmDashboardEventLaunchedEventSerializer(instance, many=True).data


class CrmDashboardEventHistogramEducationalInstitutionSerializer(
    serializers.Serializer
):
    """Serializer for educational institution enrollment data in histogram"""

    enrollments = serializers.IntegerField()
    name = serializers.CharField()
    acronym = serializers.CharField(allow_null=True)


class CrmDashboardEventHistogramEnrollmentAcquisitionTypeSerializer(
    serializers.Serializer
):
    """Serializer for enrollment acquisition type data in histogram"""

    organic = serializers.IntegerField()
    paid = serializers.IntegerField()


class CrmDashboardEventHistogramItemSerializer(serializers.Serializer):
    """Serializer for individual histogram item"""

    esid = serializers.UUIDField()
    event_name = serializers.CharField()
    start_date = serializers.DateTimeField()
    end_date = serializers.DateTimeField()
    total_enrollments = serializers.IntegerField()
    enrollment_acquisition_type = (
        CrmDashboardEventHistogramEnrollmentAcquisitionTypeSerializer()
    )
    top_educational_institutions = (
        CrmDashboardEventHistogramEducationalInstitutionSerializer(many=True)
    )
    attended_count = serializers.IntegerField()


class CrmDashboardEventHistogramSerializer(serializers.Serializer):
    """Serializer for event dashboard histogram endpoint"""

    def to_representation(self, instance):
        """Custom representation for list of histogram items"""
        if isinstance(instance, list):
            return [
                CrmDashboardEventHistogramItemSerializer(item).data for item in instance
            ]
        return CrmDashboardEventHistogramItemSerializer(instance, many=True).data

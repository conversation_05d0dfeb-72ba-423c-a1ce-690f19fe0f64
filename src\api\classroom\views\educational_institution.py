from rest_framework import viewsets
from core.models import EducationalInstitution
from api.classroom.serializers.auth import InstitutionSelectSerializer
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import TokenAuthentication


class InstitutionViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = EducationalInstitution.objects.filter(deleted=False)
    serializer_class = InstitutionSelectSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

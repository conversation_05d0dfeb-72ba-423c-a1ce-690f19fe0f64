from django_filters.rest_framework import Django<PERSON><PERSON><PERSON><PERSON><PERSON>end
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import ValidationError
from core.models import EnrollmentGrade
from api.mixins import SwaggerTagMixin
from api.permissions import IsStaffUser
from api.lms.serializers.enrollment_grade import LmsEnrollmentGradeSerializer
from api.paginations import StandardResultsPagination


class LmsEnrollmentGradeViewSet(
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    """
    ViewSet for managing student enrollments in the LMS.
    """

    queryset = EnrollmentGrade.objects.all()
    serializer_class = LmsEnrollmentGradeSerializer
    pagination_class = StandardResultsPagination
    filter_backends = (DjangoFilterBackend,)
    filterset_fields = [
        "enrollment",
        "grade",
    ]

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]

    swagger_tags = ["Enrollment Grades"]

    def create(self, request, *args, **kwargs):
        """
        Create a new EnrollmentGrade instance.
        """
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            headers = self.get_success_headers(serializer.data)
            return Response(
                serializer.data, status=status.HTTP_201_CREATED, headers=headers
            )
        except ValidationError as e:
            if e.get_codes() and e.get_codes().get("non_field_errors") == ["unique"]:
                validated_data = serializer.initial_data
                instance = EnrollmentGrade.objects.get(
                    enrollment=validated_data.get("enrollment"),
                    grade=validated_data.get("grade"),
                )

                updated_serializer = self.get_serializer(
                    instance, data=request.data, partial=True
                )
                updated_serializer.is_valid(raise_exception=True)
                self.perform_update(updated_serializer)

                return Response(updated_serializer.data, status=status.HTTP_200_OK)
            else:
                raise e

# Generated by Django 5.0.6 on 2025-09-25 22:00

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0029_attachment_is_active"),
    ]

    operations = [
        migrations.CreateModel(
            name="Grade",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=255, verbose_name="Title")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "weight",
                    models.DecimalField(
                        decimal_places=2, max_digits=5, verbose_name="Weight"
                    ),
                ),
                (
                    "max_points",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("20.00"),
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00")),
                            django.core.validators.MaxValueValidator(Decimal("100.00")),
                        ],
                        verbose_name="Max Score",
                    ),
                ),
                (
                    "offering",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="grades",
                        to="core.offering",
                        verbose_name="Offering",
                    ),
                ),
            ],
            options={
                "verbose_name": "Grade",
                "verbose_name_plural": "Grades",
            },
        ),
        migrations.CreateModel(
            name="EnrollmentGrade",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "score",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        max_digits=5,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Score",
                    ),
                ),
                (
                    "feedback",
                    models.TextField(blank=True, null=True, verbose_name="Feedback"),
                ),
                (
                    "enrollment",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="enrollment_grades",
                        to="core.studentenrollment",
                        verbose_name="Student Enrollment",
                    ),
                ),
                (
                    "grade",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="enrollment_grades",
                        to="core.grade",
                        verbose_name="Grade",
                    ),
                ),
            ],
            options={
                "verbose_name": "Enrollment Grade",
                "verbose_name_plural": "Enrollment Grades",
                "unique_together": {("enrollment", "grade")},
            },
        ),
    ]

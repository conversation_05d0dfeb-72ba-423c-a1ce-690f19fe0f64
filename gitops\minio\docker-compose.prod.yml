services:
  ceu-storage:
    image: minio/minio:RELEASE.2025-02-18T16-25-55Z
    container_name: ceu-storage
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_ACCESS_KEY}
      MINIO_ROOT_PASSWORD: ${MINIO_SECRET_KEY}
    command: server /data --console-address ":9001"
    volumes:
      - minio-data:/data
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - ceu-network

volumes:
  minio-data:

networks:
  ceu-network:
    external: true

# Name for the GitHub Actions workflow.
name: <PERSON>uild, Push to AWS ECR & Deploy to Portainer

# This workflow triggers on every push to the "main" branch.
on:
  push:
    branches: ["main"]

# Environment variables available to all jobs and steps in the workflow.
# IMPORTANT: Update these values to match your AWS setup.
env:
  AWS_REGION: "sa-east-1" # Change to your desired AWS region
  ECR_REPOSITORY_API: "portals-api"
  ECR_REPOSITORY_CELERY: "portals-celery"

jobs:
  build-and-push:
    name: Build and Push to ECR
    runs-on: ubuntu-latest

    # Permissions are needed for AWS OIDC authentication.
    permissions:
      contents: read
      id-token: write # Required for secure authentication with AWS

    steps:
      # Step 1: Check out the repository code.
      - name: Checkout repository
        uses: actions/checkout@v4

      # Step 2: Configure AWS credentials using OIDC for secure, keyless authentication.
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          # You need to create a role in AWS IAM that this action can assume.
          role-to-assume: arn:aws:iam::${{ secrets.AWS_ACCOUNT_ID }}:role/${{ secrets.AWS_IAM_ROLE_NAME }}
          aws-region: ${{ env.AWS_REGION }}

      # Step 3: Log in to Amazon ECR.
      - name: Log in to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      # Step 3.5: Set up Docker Buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Step 4: Extract a short commit hash to use as the image tag.
      # This provides a unique and traceable tag for each build.
      - name: Extract Docker image tag
        id: meta
        uses: docker/metadata-action@v5
        with:
          tags: type=sha,prefix=,format=short

      # Step 5: Build and push the API Docker image to ECR with multiple tags.
      - name: Build and push API image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./src/Dockerfile
          push: true
          # We are now creating two tags: one with the commit hash and one with 'latest'.
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY_API }}:${{ steps.meta.outputs.version }}
            ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY_API }}:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

      # Step 6: Build and push the Celery Docker image to ECR with multiple tags.
      - name: Build and push Celery image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./gitops/celery/Dockerfile.production
          push: true
          # We are now creating two tags: one with the commit hash and one with 'latest'.
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY_CELERY }}:${{ steps.meta.outputs.version }}
            ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY_CELERY }}:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-to-container:
    name: Deploy to Portainer
    needs: build-and-push
    runs-on: ubuntu-latest
    steps:
      - name: Trigger Portainer Webhook
        run: curl -X POST ${{ secrets.PORTAINER_WEBHOOK_URL }}

from rest_framework import serializers
from core.models import Term


class CrmTermSerializer(serializers.ModelSerializer):
    key = serializers.CharField(source="tid", read_only=True)

    def validate_name(self, value):
        # Normalizar: recortar espacios y poner la primera letra en mayúscula
        def normalize(v: str) -> str:
            v = v.strip()
            if not v:
                return v
            return v[0].upper() + v[1:]

        normalized = normalize(value)

        qs = Term.objects.all()
        if self.instance is not None:
            qs = qs.exclude(pk=self.instance.pk)
        # Validar duplicados exactos (case-insensitive) usando el valor normalizado
        if qs.filter(name__iexact=normalized).exists():
            raise serializers.ValidationError("Ya existe un ciclo con el mismo nombre.")
        return normalized

    class Meta:
        model = Term
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]

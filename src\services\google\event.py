import time
import random
from django.conf import settings
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError


class GoogleEventsManager:
    """
    Clase para gestionar eventos de Google utilizando la API de Google Calendar.
    """

    def __init__(self, credentials_file=None):
        """
        Inicializa el gestor de eventos con las credenciales proporcionadas.

        Args:
            credentials_file (str): Ruta al archivo JSON de la Service Account
        """

        if credentials_file is None:
            credentials_file = f"{settings.SECRET_FILES_DIR}/credentials.json"

        self.SCOPES = [
            "https://www.googleapis.com/auth/calendar",
        ]

        self.credentials = service_account.Credentials.from_service_account_file(
            credentials_file,
            scopes=self.SCOPES,
            subject="<EMAIL>",
        )
        self.service = build("calendar", "v3", credentials=self.credentials)
        self.events_service = self.service.events()

    def list_events(self, calendar_id="primary", page_size=100):
        """
        Obtiene la lista de eventos del calendario especificado.

        Args:
            calendar_id (str): ID del calendario del que se desean obtener los eventos
            page_size (int): Número máximo de eventos a recuperar

        Returns:
            list: Lista de eventos
        """
        try:
            results = self.events_service.list(
                calendarId=calendar_id,
                maxResults=page_size,
                singleEvents=True,
                orderBy="startTime",
            ).execute()
            return results.get("items", [])
        except HttpError as error:
            print(f"Error al listar eventos: {error}")
            return []
        except Exception as error:
            print(f"Error inesperado al listar eventos: {error}")
            return []

    def list_calendars(self):
        """
        Lista todos los calendarios disponibles.

        Returns:
            list: Lista de calendarios
        """
        try:
            results = self.service.calendarList().list().execute()
            return results.get("items", [])
        except HttpError as error:
            print(f"Error al listar calendarios: {error}")
            return []

    def create_event(
        self,
        calendar_id="primary",
        summary=None,
        description=None,
        start_datetime=None,
        end_datetime=None,
        timezone="America/Lima",
        attendees=None,
        location=None,
        create_meet=False,
    ):
        """
        Crea un nuevo evento en el calendario especificado.

        Args:
            calendar_id (str): ID del calendario donde crear el evento
            summary (str): Título del evento
            description (str, optional): Descripción del evento
            start_datetime (str): Fecha y hora de inicio (formato ISO)
            end_datetime (str): Fecha y hora de fin (formato ISO)
            timezone (str): Zona horaria del evento
            attendees (list, optional): Lista de correos de asistentes
            location (str, optional): Ubicación del evento

        Returns:
            dict: Datos del evento creado o None si hay error
        """
        try:
            event_body = {
                "summary": summary,
                "start": {
                    "dateTime": start_datetime,
                    "timeZone": timezone,
                },
                "end": {
                    "dateTime": end_datetime,
                    "timeZone": timezone,
                },
            }

            if description:
                event_body["description"] = description

            if location:
                event_body["location"] = location

            if attendees:
                event_body["attendees"] = [{"email": email} for email in attendees]

            if create_meet:
                event_body["conferenceData"] = {
                    "createRequest": {
                        "requestId": f"meet-{int(time.time())}-{random.randint(1000, 9999)}",
                        "conferenceSolutionKey": {"type": "hangoutsMeet"},
                    }
                }

            # Importante: Usar conferenceDataVersion=1 cuando se incluye conferenceData
            conference_version = 1 if create_meet else 0

            result = self.events_service.insert(
                calendarId=calendar_id,
                body=event_body,
                conferenceDataVersion=conference_version,
            ).execute()

            return result
        except HttpError as error:
            print(f"Error al crear evento: {error}")
            return None

    def get_event(self, calendar_id="primary", event_id=None):
        """
        Obtiene los detalles de un evento específico.

        Args:
            calendar_id (str): ID del calendario
            event_id (str): ID del evento

        Returns:
            dict: Datos del evento o None si hay error
        """
        try:
            result = self.events_service.get(
                calendarId=calendar_id, eventId=event_id
            ).execute()

            return result
        except HttpError as error:
            print(f"Error al obtener evento: {error}")
            return None

    def update_event(
        self,
        calendar_id="primary",
        event_id=None,
        summary=None,
        description=None,
        start_datetime=None,
        end_datetime=None,
        timezone="America/Lima",
        attendees=None,
        location=None,
    ):
        """
        Actualiza un evento existente.

        Args:
            calendar_id (str): ID del calendario
            event_id (str): ID del evento a actualizar
            summary (str, optional): Nuevo título del evento
            description (str, optional): Nueva descripción
            start_datetime (str, optional): Nueva fecha y hora de inicio
            end_datetime (str, optional): Nueva fecha y hora de fin
            timezone (str): Zona horaria del evento
            attendees (list, optional): Nueva lista de asistentes
            location (str, optional): Nueva ubicación

        Returns:
            dict: Datos del evento actualizado o None si hay error
        """
        try:
            # Primero obtenemos el evento actual
            event = self.get_event(calendar_id, event_id)
            if not event:
                return None

            # Solo actualizamos los campos que se proporcionan
            if summary:
                event["summary"] = summary

            if description is not None:  # Permitir descripción vacía
                event["description"] = description

            if start_datetime:
                event["start"] = {
                    "dateTime": start_datetime,
                    "timeZone": timezone,
                }

            if end_datetime:
                event["end"] = {
                    "dateTime": end_datetime,
                    "timeZone": timezone,
                }

            if location is not None:  # Permitir ubicación vacía
                event["location"] = location

            if attendees is not None:  # Permitir lista vacía
                event["attendees"] = [{"email": email} for email in attendees]

            result = self.events_service.update(
                calendarId=calendar_id, eventId=event_id, body=event
            ).execute()

            return result
        except HttpError as error:
            print(f"Error al actualizar evento: {error}")
            return None

    def delete_event(self, calendar_id="primary", event_id=None):
        """
        Elimina un evento.

        Args:
            calendar_id (str): ID del calendario
            event_id (str): ID del evento a eliminar

        Returns:
            bool: True si la eliminación fue exitosa, False en caso contrario
        """
        try:
            self.events_service.delete(
                calendarId=calendar_id, eventId=event_id
            ).execute()
            return True
        except HttpError as error:
            print(f"Error al eliminar evento: {error}")
            return False

    def search_events(
        self,
        calendar_id="primary",
        query=None,
        time_min=None,
        time_max=None,
        page_size=100,
    ):
        """
        Busca eventos que coincidan con los criterios especificados.

        Args:
            calendar_id (str): ID del calendario
            query (str, optional): Texto a buscar en los eventos
            time_min (str, optional): Fecha mínima (formato ISO)
            time_max (str, optional): Fecha máxima (formato ISO)
            page_size (int): Número máximo de resultados

        Returns:
            list: Lista de eventos que coinciden con la búsqueda
        """
        try:
            params = {
                "calendarId": calendar_id,
                "maxResults": page_size,
                "singleEvents": True,
                "orderBy": "startTime",
            }

            if query:
                params["q"] = query

            if time_min:
                params["timeMin"] = time_min

            if time_max:
                params["timeMax"] = time_max

            results = self.events_service.list(**params).execute()
            return results.get("items", [])
        except HttpError as error:
            print(f"Error al buscar eventos: {error}")
            return []

    def add_attendee_to_event(self, event_id, attendee_email, calendar_id="primary"):
        """
        Añade un asistente a un evento existente en Google Calendar.

        Args:
            event_id (str): ID del evento en Google Calendar (ext_event_reference)
            attendee_email (str): Email del asistente a añadir
            calendar_id (str): ID del calendario (por defecto "primary")

        Returns:
            dict: Evento actualizado o None si hay error
        """
        try:
            # Primero obtener el evento actual
            event = self.events_service.get(
                calendarId=calendar_id, eventId=event_id
            ).execute()

            # Verificar si ya existe la lista de asistentes
            attendees = event.get("attendees", [])

            # Verificar si el email ya está en la lista de asistentes
            existing_emails = [
                attendee.get("email", "").lower() for attendee in attendees
            ]
            if attendee_email.lower() in existing_emails:
                print(f"El asistente {attendee_email} ya está en el evento {event_id}")
                return event

            # Añadir el nuevo asistente
            new_attendee = {
                "email": attendee_email,
                "responseStatus": "needsAction",  # Estado inicial
            }
            attendees.append(new_attendee)

            # Actualizar el evento con la nueva lista de asistentes
            event["attendees"] = attendees

            # Enviar la actualización a Google Calendar
            updated_event = self.events_service.update(
                calendarId=calendar_id,
                eventId=event_id,
                body=event,
                sendUpdates="all",  # Envía invitación por email automáticamente
            ).execute()

            print(
                f"Asistente {attendee_email} añadido exitosamente al evento {event_id}"
            )
            return updated_event

        except HttpError as error:
            print(f"Error al añadir asistente al evento {event_id}: {error}")
            return None
        except Exception as error:
            print(f"Error inesperado al añadir asistente: {error}")
            return None

    def add_multiple_attendees_to_event(
        self, event_id, attendee_emails, calendar_id="primary"
    ):
        """
        Añade múltiples asistentes a un evento existente en Google Calendar en una sola llamada.
        Esto evita saturar la API con múltiples llamadas individuales.

        Args:
            event_id (str): ID del evento en Google Calendar
            attendee_emails (list): Lista de emails de asistentes a añadir
            calendar_id (str): ID del calendario (por defecto "primary")

        Returns:
            dict: Evento actualizado o None si hay error
        """
        try:
            # Primero obtener el evento actual
            event = self.events_service.get(
                calendarId=calendar_id, eventId=event_id
            ).execute()

            # Verificar si ya existe la lista de asistentes
            existing_attendees = event.get("attendees", [])
            existing_emails = [
                attendee.get("email", "").lower() for attendee in existing_attendees
            ]

            # Filtrar emails que ya están en el evento
            new_attendees = []
            added_emails = []

            for email in attendee_emails:
                if email.lower() not in existing_emails:
                    new_attendees.append(
                        {"email": email, "responseStatus": "needsAction"}
                    )
                    added_emails.append(email)
                else:
                    print(f"El asistente {email} ya está en el evento {event_id}")

            if not new_attendees:
                print(f"Todos los asistentes ya están en el evento {event_id}")
                return event

            # Añadir los nuevos asistentes a la lista existente
            all_attendees = existing_attendees + new_attendees
            event["attendees"] = all_attendees

            # Enviar la actualización a Google Calendar
            updated_event = self.events_service.update(
                calendarId=calendar_id,
                eventId=event_id,
                body=event,
                sendUpdates="all",  # Envía invitación por email automáticamente
            ).execute()

            print(
                f"Asistentes {added_emails} añadidos exitosamente al evento {event_id}"
            )
            return updated_event

        except HttpError as error:
            print(f"Error al añadir múltiples asistentes al evento {event_id}: {error}")
            return None
        except Exception as error:
            print(f"Error inesperado al añadir múltiples asistentes: {error}")
            return None

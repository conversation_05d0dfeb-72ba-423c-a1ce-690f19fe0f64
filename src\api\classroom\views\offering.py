from rest_framework import viewsets
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from core.models import Offering
from core.models import Topic
from api.classroom.serializers.offering import (
    ClassroomOfferingBaseSerializer,
    ClassroomOfferingDetailedSerializer,
    TopicSerializer,
)


class ClassroomOfferingViewset(viewsets.ModelViewSet):
    model_class = Offering
    queryset = Offering.objects.all()
    authentication_classes = [TokenAuthentication]
    serializer_class = ClassroomOfferingBaseSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action == "retrieve":
            return ClassroomOfferingDetailedSerializer
        return ClassroomOfferingBaseSerializer

    @action(
        detail=False,
        methods=["get"],
        url_path=r"topic/(?P<tid>[^/.]+)",
        url_name="topic-detail",
    )
    def topic_detail(self, request, tid=None):
        topic = get_object_or_404(Topic, tid=tid)
        serializer = TopicSerializer(topic, context={"request": request})
        return Response(serializer.data)

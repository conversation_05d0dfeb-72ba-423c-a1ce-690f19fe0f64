import logging
from typing import Optional, List, Dict, Any

from google.auth.transport.requests import Request
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from django.conf import settings

logger = logging.getLogger(__name__)


class GoogleMeetAttendanceService:
    def __init__(self, credentials_file: Optional[str] = None):
        if credentials_file is None:
            credentials_file = f"{settings.SECRET_FILES_DIR}/credentials.json"

        self.SCOPES = [
            "https://www.googleapis.com/auth/admin.reports.audit.readonly",
            "https://www.googleapis.com/auth/admin.reports.usage.readonly",
        ]

        try:
            self.credentials = service_account.Credentials.from_service_account_file(
                credentials_file,
                scopes=self.SCOPES,
                subject="<EMAIL>",
            )

            if self.credentials.expired:
                self.credentials.refresh(Request())

            self.service = build("admin", "reports_v1", credentials=self.credentials)
            self.attendance_service = self.service.activities()

        except Exception as e:
            logger.error(f"Error initializing Google service: {e}")
            raise

    def get_meet_attendance(self, event_id: str):
        try:
            if self.credentials.expired:
                self.credentials.refresh(Request())

            results = (
                self.attendance_service.list(
                    userKey="all",
                    applicationName="meet",
                    filters=f"meeting_code=={event_id}",
                )
                .execute()
                .get("items", [])
            )
            return results

        except HttpError as error:
            if error.resp.status == 403:
                logger.error(
                    f"Forbidden: Check domain-wide delegation and user permissions. Error: {error}"
                )
            elif error.resp.status == 401:
                logger.error(
                    f"Unauthorized: Check service account credentials and scopes. Error: {error}"
                )
            else:
                logger.error(f"HTTP Error occurred: {error}")
            return None

        except Exception as error:
            logger.error(f"Unexpected error occurred: {error}")
            return None

    def get_attendees_list(self, event_id: str) -> List[Dict[str, Any]]:
        """
        Gets a processed and cleaned list of attendees with name and email.
        """
        raw_data = self.get_meet_attendance(event_id)
        if not raw_data:
            return []

        attendees = {}  # Use dict to avoid duplicates by email

        for activity in raw_data:
            try:
                # The email can be in the actor (internal users) or in the parameters (external)
                final_email = activity.get("actor", {}).get("email")
                display_name = ""

                # If there is no email in 'actor', look for it in the parameters
                if not final_email:
                    for meet_event in activity.get("events", []):
                        # Convert the parameters to a dictionary for easy access
                        params = {
                            p.get("name"): p.get("value")
                            for p in meet_event.get("parameters", [])
                        }

                        identifier = params.get("identifier")
                        identifier_type = params.get("identifier_type")

                        if identifier and identifier_type == "email_address":
                            final_email = identifier
                            display_name = params.get("display_name", "")
                            break  # Exit the events loop once found

                # If no email was found, skip this record
                if not final_email:
                    continue

                # If we still don't have the name, look for it (internal users case)
                if not display_name:
                    for meet_event in activity.get("events", []):
                        params = {
                            p.get("name"): p.get("value")
                            for p in meet_event.get("parameters", [])
                        }
                        if params.get("display_name"):
                            display_name = params.get("display_name")
                            break

                # If there's still no name, use the email alias
                final_name = display_name or final_email.split("@")[0]

                # Add to the attendees list if it doesn't exist
                if final_email not in attendees:
                    attendees[final_email] = {
                        "email": final_email,
                        "name": final_name,
                    }

            except Exception as e:
                logger.warning(f"Error processing activity: {e}")
                continue

        return list(attendees.values())

from rest_framework import routers
from api.shared.views import payment as payment_views
from api.shared.views import file as file_views
from api.shared.views import celery as celery_views

router = routers.DefaultRouter(trailing_slash=False)

router.register(
    r"payments",
    payment_views.SharedPaymentViewSet,
    basename="shared-payments",
)

router.register(
    r"files",
    file_views.SharedFileViewSet,
    basename="shared-files",
)

router.register(
    r"celery",
    celery_views.CeleryViewSet,
    basename="celery",
)

urlpatterns = router.urls

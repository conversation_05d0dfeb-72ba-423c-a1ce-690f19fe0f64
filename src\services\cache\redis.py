"""
Redis Cache utilities for the API
"""

import hashlib
from functools import wraps
from django.core.cache import cache


def get_cache_version(prefix):
    """
    Get current cache version for a prefix
    """
    version_key = f"{prefix}:version"
    version = cache.get(version_key)
    if version is None:
        version = 1
        cache.set(version_key, version, None)  # No expiry for version keys
    return version


def bump_cache_version(prefix):
    """
    Increment cache version for a prefix, effectively invalidating all keys with that prefix
    """
    version_key = f"{prefix}:version"
    version = cache.get(version_key) or 1
    cache.set(version_key, version + 1, None)  # No expiry for version keys
    return version + 1


def generate_cache_key(prefix, *args, **kwargs):
    """
    Generate a cache key based on prefix, version, and arguments
    """
    version = get_cache_version(prefix)
    key_parts = [prefix, f"v{version}"]

    # Add positional arguments
    for arg in args:
        key_parts.append(str(arg))

    # Add keyword arguments (sorted for consistency)
    for key, value in sorted(kwargs.items()):
        key_parts.append(f"{key}:{value}")

    # Create the key
    key = ":".join(key_parts)

    # If key is too long, hash it
    if len(key) > 200:
        key_hash = hashlib.md5(key.encode()).hexdigest()
        key = f"{prefix}:v{version}:hash:{key_hash}"

    return key


def cache_data(key_prefix, timeout=300):
    """
    Decorator to cache data

    Args:
        key_prefix: Prefix for cache key
        timeout: Cache timeout in seconds (default: 5 minutes)
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key based on function name and arguments
            cache_key = generate_cache_key(
                key_prefix, func.__name__, *args[1:], **kwargs  # Skip 'self' argument
            )

            # Try to get from cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result

            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)

            return result

        return wrapper

    return decorator


def invalidate_cache(key_prefix):
    """
    Invalidate all cache keys with given prefix using versioning.
    This is fast and efficient as it only bumps the version number.

    Args:
        key_prefix: The prefix to invalidate

    Returns:
        New version number
    """
    return bump_cache_version(key_prefix)


class CacheManager:
    """
    Manager for cache operations using django-redis
    """

    def __init__(self, prefix, timeout=None):
        self.prefix = prefix
        self.timeout = timeout

    def get_cache_key(self, endpoint, **filters):
        """
        Generate cache key for dashboard endpoint
        """
        return generate_cache_key(self.prefix, endpoint, **filters)

    def get(self, endpoint, **filters):
        """
        Get cached data for dashboard endpoint
        """
        cache_key = self.get_cache_key(endpoint, **filters)
        return cache.get(cache_key)

    def set(self, endpoint, data, timeout=300, **filters):
        """
        Set cached data for endpoint

        Args:
            endpoint: Cache endpoint identifier
            data: Data to cache
            timeout: Cache timeout in seconds (default: 5 minutes)
                    Set to None for no expiry, 0 to use default cache timeout
                    If a global timeout is set, it will be used instead
            **filters: Additional filters for cache key generation
        """
        cache_key = self.get_cache_key(endpoint, **filters)

        # If a general timeout is set, use it
        if self.timeout:
            timeout = self.timeout

        cache.set(cache_key, data, timeout)

    def invalidate(self):
        """
        Invalidate all cache keys with this prefix using versioning.
        This is fast and safe as it only bumps the version number.
        """
        return bump_cache_version(self.prefix)

    def invalidate_specific(self, endpoint, **filters):
        """
        Invalidate a specific cache key directly (granular invalidation)
        """
        cache_key = self.get_cache_key(endpoint, **filters)
        return cache.delete(cache_key)

    def get_version(self):
        """
        Get current cache version for this prefix
        """
        return get_cache_version(self.prefix)

    def debug_cache_info(self, endpoint=None, **filters):
        """
        Debug method to check cache key info and TTL
        """
        if endpoint:
            cache_key = self.get_cache_key(endpoint, **filters)
            cached_data = cache.get(cache_key)
            # Try to get TTL (django-redis specific)
            try:
                ttl = cache.ttl(cache_key)
            except:
                ttl = "TTL not supported by backend"

            return {
                "cache_key": cache_key,
                "exists": cached_data is not None,
                "ttl_seconds": ttl,
                "version": self.get_version(),
            }
        else:
            version_key = f"{self.prefix}:version"
            try:
                version_ttl = cache.ttl(version_key)
            except:
                version_ttl = "TTL not supported"

            return {
                "prefix": self.prefix,
                "version": self.get_version(),
                "version_key": version_key,
                "version_ttl": version_ttl,
            }

import uuid
from django.db import models
from core.models.base import AuditBaseModel


class BlogPost(AuditBaseModel):
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"

    STATUS_CHOICES = [
        (DRAFT, "Draft"),
        (PUBLISHED, "Published"),
        (ARCHIVED, "Archived"),
    ]

    bid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    slug = models.SlugField(
        max_length=255,
        blank=True,
        unique=True,
        verbose_name="Slug",
    )
    title = models.CharField(
        max_length=255,
        blank=True,
        verbose_name="Title",
    )

    summary = models.TextField(blank=True, verbose_name="Summary")
    content = models.JSONField(blank=True, null=True, verbose_name="Content")

    cover_image = models.ForeignKey(
        "File",
        on_delete=models.SET_NULL,
        related_name="blog_covers",
        blank=True,
        null=True,
        verbose_name="Cover Image",
    )
    thumbnail = models.ForeignKey(
        "File",
        on_delete=models.SET_NULL,
        related_name="blog_thumbnails",
        blank=True,
        null=True,
        verbose_name="Thumbnail",
    )

    reading_time = models.PositiveIntegerField(
        blank=True, null=True, verbose_name="Reading Time (minutes)"
    )

    status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default=DRAFT,
        verbose_name="Status",
    )

    published_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="Published At",
    )

    # Visualization
    featured = models.BooleanField(
        default=False,
        verbose_name="Featured",
    )

    featured_order = models.PositiveIntegerField(
        blank=True, null=True, verbose_name="Featured Order"
    )

    # SEO
    meta_title = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="Meta Title",
    )
    meta_description = models.TextField(
        blank=True,
        null=True,
        verbose_name="Meta Description",
    )
    meta_keywords = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="Meta Keywords",
    )

    # Analytics
    view_count = models.PositiveIntegerField(default=0, verbose_name="View Count")

    # Relations
    created_by = models.ForeignKey(
        "core.User",
        on_delete=models.SET_NULL,
        related_name="blog_posts",
        blank=True,
        null=True,
        verbose_name="Created By",
    )

    authors = models.ManyToManyField(
        "Instructor",
        related_name="blog_posts",
        blank=True,
        verbose_name="Authors",
    )

    categories = models.ManyToManyField(
        "BlogCategory",
        related_name="blog_posts",
        blank=True,
        verbose_name="Categories",
    )

    tags = models.ManyToManyField(
        "BlogTag",
        related_name="blog_posts",
        blank=True,
        verbose_name="Tags",
    )

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = "Blog Post"
        verbose_name_plural = "Blog Posts"

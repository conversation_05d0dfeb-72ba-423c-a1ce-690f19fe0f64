from celery import shared_task
from core.models import Order, OrderItem
from services.google.classroom import GoogleClassroomManager


@shared_task
def send_classroom_invitation(email: str, class_code: str, order_item_id: str = None):
    """
    Tarea para enviar invitación a Google Classroom.

    Args:
        email (str): Email del estudiante
        class_code (str): Código de la clase de Google Classroom
        order_item_id (str, optional): ID del OrderItem para actualizar el estado
    """
    order_item = None

    try:
        # Obtener el OrderItem si se proporciona
        if order_item_id:
            order_item = OrderItem.objects.get(id=order_item_id)
            # Actualizar estado a "enviando"
            order_item.ext_invitation_status = OrderItem.INVITATION_SENDING
            order_item.save(update_fields=["ext_invitation_status"])

        classroom_manager = GoogleClassroomManager()

        course = classroom_manager.get_course_by_enrollment_code(class_code)
        course_id = course.get("id") if course else None

        if not course_id:
            raise Exception(f"No se encontró curso con código: {class_code}")

        # Enviar la invitación a Google Classroom
        invitation_result = classroom_manager.invite_student_to_course(course_id, email)

        if not invitation_result:
            raise Exception(
                f"No se pudo enviar la invitación de Google Classroom a {email}"
            )

        # Si la invitación fue exitosa, actualizar el estado del OrderItem
        if order_item:
            order_item.ext_invitation_status = OrderItem.INVITATION_SENT
            order_item.save(update_fields=["ext_invitation_status"])

        return {
            "success": True,
            "message": f"Invitación enviada exitosamente a {email}",
            "invitation_status": invitation_result.get("status", "sent"),
            "order_item_id": order_item_id,
        }

    except Exception as e:
        # Si hay error, actualizar el estado del OrderItem
        if order_item:
            order_item.ext_invitation_status = OrderItem.INVITATION_ERROR
            order_item.save(update_fields=["ext_invitation_status"])

        error_msg = f"Error al enviar invitación de Google Classroom: {str(e)}"
        print(error_msg)
        raise Exception(error_msg)


@shared_task
def process_classroom_invitation_for_order(order_id, delay=True):
    """
    Procesa la invitación de Google Classroom para una orden específica.
    Verifica que la orden tenga productos con referencia externa (ext_reference)
    y envía las invitaciones correspondientes.

    Args:
        order_id (str): ID de la orden
    """
    try:
        order = Order.objects.get(oid=order_id)
        user = order.owner

        # Validar que el usuario tenga datos completos
        if not user.first_name or not user.last_name:
            return {
                "success": False,
                "message": "El usuario debe tener nombre y apellido completos",
            }

        if not user.email or not user.email.lower().endswith("@gmail.com"):
            return {
                "success": False,
                "message": "El usuario debe tener un correo de Gmail válido",
            }

        # Obtener todos los items de la orden
        order_items = order.items.filter(deleted=False)

        invitations_sent = []

        for item in order_items:
            offering = item.offering

            # Verificar si el offering tiene referencia externa (course_id)
            if offering.ext_reference:
                try:
                    print(f"Invitando a {item.id}")
                    # Enviar invitación para este curso
                    if delay:
                        result = send_classroom_invitation.delay(
                            email=user.email,
                            class_code=offering.ext_reference,
                            order_item_id=str(item.id),
                        )
                        invitations_sent.append(
                            {
                                "offering_name": offering.name,
                                "class_code": offering.ext_reference,
                                "task_id": result.id,
                                "order_item_id": str(item.id),
                            }
                        )

                    else:
                        send_classroom_invitation(
                            email=user.email,
                            class_code=offering.ext_reference,
                            order_item_id=str(item.id),
                        )
                        invitations_sent.append(
                            {
                                "offering_name": offering.name,
                                "class_code": offering.ext_reference,
                                "order_item_id": str(item.id),
                                "success": True,
                            }
                        )

                except Exception as e:
                    # print(
                    #     f"Error al procesar invitación para {offering.name}: {str(e)}"
                    # )
                    item.ext_invitation_status = OrderItem.INVITATION_ERROR
                    item.save(update_fields=["ext_invitation_status"])
                    continue
            else:
                # Error al enviar invitación: no tiene referencia externa
                item.ext_invitation_status = OrderItem.INVITATION_ERROR
                item.save(update_fields=["ext_invitation_status"])

        if not invitations_sent:
            return {
                "success": False,
                "message": "No se enviaron todas las invitaciones. Verificar los códigos de clase de cada programa.",
                "invitations_sent": [],
            }

        return {
            "success": True,
            "message": f"Se procesaron {len(invitations_sent)} invitaciones",
            "invitations_sent": invitations_sent,
        }

    except Order.DoesNotExist:
        error_msg = f"Orden con ID {order_id} no encontrada"
        print(error_msg)
        raise Exception(error_msg)
    except Exception as e:
        error_msg = f"Error al procesar invitaciones de Google Classroom: {str(e)}"
        print(error_msg)
        raise Exception(error_msg)

from rest_framework import viewsets, filters
from core.models import LeadOrigin
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.serializers.lead_origin import (
    CrmLeadOriginSerializer,
    CrmCreateLeadOriginSerializer,
)
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser
from django_filters.rest_framework import DjangoFilterBackend


class CrmLeadOriginViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    class LeadOriginPagination(StandardResultsPagination):
        page_size = 20

    model_class = LeadOrigin
    queryset = LeadOrigin.objects.filter(deleted=False).order_by("created_at")
    swagger_tags = ["Lead Origin"]
    pagination_class = LeadOriginPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    )
    ordering_fields = ["created_at", "name"]
    search_fields = [
        "name",
        "description",
    ]

    def get_serializer_class(self, *args, **kwargs):
        if self.action == "create":
            return CrmCreateLeadOriginSerializer
        return CrmLeadOriginSerializer

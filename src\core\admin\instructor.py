from django.contrib import admin
from core.models import Instructor, Attachment


@admin.register(Instructor)
class InstructorAdmin(admin.ModelAdmin):
    readonly_fields = [
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]

    list_display = [
        "full_name",
        "status",
        "profile_photo",
        "created_at",
        "updated_at",
        "deleted",
    ]
    list_filter = (
        "status",
        "deleted",
    )
    search_fields = ("title",)


@admin.register(Attachment)
class AttachmentAdmin(admin.ModelAdmin):
    list_display = [
        "aid",
        "get_instructor_name",
        "get_offering_name",
        "is_active",
        "created_at",
        "updated_at",
    ]
    list_filter = ("is_active", "instructor", "offering")
    search_fields = ("instructor__full_name", "offering__name")

    def get_instructor_name(self, obj):
        return obj.instructor.full_name if obj.instructor else "N/A"

    get_instructor_name.short_description = "Instructor"
    get_instructor_name.admin_order_field = "instructor__full_name"

    def get_offering_name(self, obj):
        return obj.offering.name if obj.offering else "N/A"

    get_offering_name.short_description = "Offering"
    get_offering_name.admin_order_field = "offering__name"

from enum import Enum
from rest_framework import serializers
from core.models import Payment


class PaymentStatusEnum(Enum):
    LOST = "lost"
    PAID = "paid"
    PENDING = "pending"

    @classmethod
    def choices(cls):
        return [(tag.value, tag.name) for tag in cls]


class FullPaymentSummarySerializer(serializers.Serializer):
    """
    Serializador único para historial de pagos y resumen financiero.
    """

    payments = serializers.SerializerMethodField()
    total_paid = serializers.DecimalField(max_digits=10, decimal_places=2)
    pending_payments = serializers.DecimalField(max_digits=10, decimal_places=2)
    next_payment_date = serializers.DateTimeField(allow_null=True)

    def get_payments(self, obj):
        """
        Serializa todos los pagos individuales.
        """
        payments = obj.get("payments", [])

        def serialize_payment(p):
            if p.order and p.order.oid:
                oid_str = str(p.order.oid)[-6:].zfill(6)
                concept = f"Order#{oid_str}"
            else:
                concept = "Unlinked payment"

            if p.is_lost:
                status = PaymentStatusEnum.LOST.value
            elif p.is_paid:
                status = PaymentStatusEnum.PAID.value
            else:
                status = PaymentStatusEnum.PENDING.value

            return {
                "pid": p.pid,
                "concept": concept,
                "status": status,
                "date": (p.payment_date or p.scheduled_payment_date),
                "amount": f"{p.currency.upper()} {p.amount}",
            }

        return [serialize_payment(p) for p in payments]

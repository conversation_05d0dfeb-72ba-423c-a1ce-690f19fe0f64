from rest_framework import viewsets, filters
from core.models import ContactChannel
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.serializers.contact_channel import (
    CrmContactChannelSerializer,
    CrmCreateContactChannelSerializer,
)
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser
from django_filters.rest_framework import DjangoFilterBackend


class CrmContactChannelViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    class ContactChannelPagination(StandardResultsPagination):
        page_size = 20

    model_class = ContactChannel
    queryset = ContactChannel.objects.filter(deleted=False).order_by("created_at")
    swagger_tags = ["Contact Channel"]
    pagination_class = ContactChannelPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]

    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    )
    ordering_fields = ["created_at", "name"]
    search_fields = [
        "name",
        "description",
    ]

    def get_serializer_class(self, *args, **kwargs):
        if self.action == "create":
            return CrmCreateContactChannelSerializer
        return CrmContactChannelSerializer

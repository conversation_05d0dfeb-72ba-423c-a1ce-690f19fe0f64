from rest_framework import serializers
from core.models import LeadOrigin


class CrmLeadOriginSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeadOrigin
        fields = (
            "loid",
            "name",
            "description",
            "created_at",
            "updated_at",
        )


class CrmCreateLeadOriginSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeadOrigin
        fields = [
            "name",
            "description",
        ]

    def validate_name(self, value):
        value = value.strip()
        if LeadOrigin.objects.filter(name__iexact=value).exists():
            raise serializers.ValidationError(
                "Este registro ya existe. Si no lo encuentras, contacta con el administrador para restaurarlo."
            )
        return value

    def create(self, validated_data):
        return LeadOrigin.objects.create(**validated_data)

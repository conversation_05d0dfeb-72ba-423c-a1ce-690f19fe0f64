from api.automations.constants.event import (
    SPECIALIZATION_OPTIONS,
    SPECIALIZATION_PREFIX_MAP,
    CEUS_OPTIONS,
    DIFUSSION_MEDIUM_MAPPING,
)
from api.automations.utils.text_processing import (
    similarity_ratio,
)


def replace_prefix(option):
    parts = option.split(" ", 1)
    if len(parts) == 2 and parts[0] in SPECIALIZATION_PREFIX_MAP:
        return f"{SPECIALIZATION_PREFIX_MAP[parts[0]]} {parts[1]}"
    return option


def map_interest_to_specialization_levenshtein(interest, threshold=0.7):
    interest_norm = interest.lower().replace("_", " ")

    # caso especial mientras se normalizan las opciones del form de meta
    if "cursos de extensión" in interest_norm:
        for option in SPECIALIZATION_OPTIONS:
            if "cursos de extensión" in option.lower():
                return option

    best_match = None
    best_score = 0

    for option in SPECIALIZATION_OPTIONS:
        option_mapped = replace_prefix(option)
        option_norm = option_mapped.lower()
        score = similarity_ratio(interest_norm, option_norm)
        print(f"score: {score} | comparando: '{interest_norm}' vs '{option_norm}'")
        if score > best_score and score >= threshold:
            best_score = score
            best_match = option
    return best_match


def map_acronym_to_ceus_option(acronym):
    """
    Mapea el acronimo de la CEU a su opción en el formulario de meta
    Nota: Los acrónimos siempre van en mayúsculas y están al inicio de la cadena
    """
    acronym = acronym.strip().upper()
    for option in CEUS_OPTIONS:
        if option.upper().startswith(acronym):
            return option
    return None


def map_diffusion_channel(value):
    """
    Mapea el valor recibido al nombre real del canal de difusión usando DIFUSSION_MEDIUM_MAPPING.
    Si no hay match, retorna el valor original.
    """
    if not value:
        return value
    value_norm = value.strip().lower()
    mapped = DIFUSSION_MEDIUM_MAPPING.get(value_norm)
    if mapped:
        return mapped
    return value

"""
Handle team channel invitations
currently using Evolution API
"""

from core.models import Order, OrderItem
from api.crm.services.evolution_api import EvolutionAPIClient, EvolutionAPIError
from django.conf import settings

import logging
from celery import shared_task

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def process_team_channel_invitation(self, order_item_id=None):
    try:
        print("Processing team channel invitation")
        # Get the order item
        order_item = OrderItem.objects.get(id=order_item_id)
        offering = order_item.offering

        if not offering.team_channel_id or offering.team_channel_id == "":
            logger.info(f"No team channel ID found for offering {offering.name}")
            return {
                "success": False,
                "message": f"No team channel ID found for offering {offering.name}",
                "order_item_id": str(order_item.id),
            }

        order_item.team_channel_invitation_status = (
            OrderItem.TEAM_CHANNEL_INVITATION_PENDING
        )
        order_item.save(update_fields=["team_channel_invitation_status"])

        # Get the order
        order = order_item.order
        user = order.owner
        phone_number = user.phone_number

        if not phone_number:
            logger.error(f"No phone number found for user {user.email}")
            order_item.team_channel_invitation_status = (
                OrderItem.TEAM_CHANNEL_INVITATION_ERROR
            )
            order_item.save(update_fields=["team_channel_invitation_status"])
            return {
                "success": False,
                "message": "No se encontró número de teléfono para el usuario",
                "order_item_id": str(order_item.id),
            }

        # Get instance name from settings
        instance_name = settings.EVOLUTION_API_INSTANCE_NAME
        if not instance_name:
            logger.error("EVOLUTION_API_INSTANCE_NAME not configured")
            order_item.team_channel_invitation_status = (
                OrderItem.TEAM_CHANNEL_INVITATION_ERROR
            )
            order_item.save(update_fields=["team_channel_invitation_status"])
            return {
                "success": False,
                "message": "Servicio de mensajería no configurado",
                "order_item_id": str(order_item.id),
            }

        evo_client = EvolutionAPIClient(instance_name)

        self.update_state(
            state="PROGRESS",
            meta={"message": "Añadiendo al grupo"},
        )

        # Call Evolution API to add participant to group
        response = evo_client.groups.update_participants(
            group_jid=offering.team_channel_id,
            action="add",
            participants=[phone_number],
        )

        # Handle the response
        if response.get("success") is True:
            # Check the actual API response data for participant status
            response_data = response.get("data", {})
            update_participants = response_data.get("updateParticipants", [])

            if update_participants:
                participant_status = update_participants[0].get("status", "")

                if participant_status == "200":
                    # Successfully added to group
                    order_item.team_channel_invitation_status = (
                        OrderItem.TEAM_CHANNEL_INVITATION_SUCCESS
                    )
                    order_item.save(update_fields=["team_channel_invitation_status"])

                    logger.info(
                        f"Successfully added user {user.email} to team channel {offering.team_channel_id}"
                    )
                    return {
                        "success": True,
                        "message": "Añadido al grupo",
                        "order_item_id": str(order_item.id),
                        "phone_number": phone_number,
                        "team_channel_id": offering.team_channel_id,
                    }

                # if status 409, the user is already in the channel
                elif participant_status == "409":
                    order_item.team_channel_invitation_status = (
                        OrderItem.TEAM_CHANNEL_INVITATION_SUCCESS
                    )
                    order_item.save(update_fields=["team_channel_invitation_status"])

                    logger.info(
                        f"User with phone number {phone_number} is already in the channel {offering.team_channel_id}"
                    )
                    return {
                        "success": True,
                        "message": "Añadido al grupo",
                        "order_item_id": str(order_item.id),
                        "phone_number": phone_number,
                        "team_channel_id": offering.team_channel_id,
                    }

                elif participant_status == "403":
                    # Forbidden - user needs to accept invitation or change privacy settings
                    order_item.team_channel_invitation_status = (
                        OrderItem.TEAM_CHANNEL_INVITATION_FORBIDDEN
                    )
                    order_item.save(update_fields=["team_channel_invitation_status"])

                    logger.warning(
                        f"Forbidden to add user {user.email} to team channel {offering.team_channel_id}"
                    )
                    return {
                        "success": False,
                        "message": "No permitido",
                        "order_item_id": str(order_item.id),
                        "status": OrderItem.TEAM_CHANNEL_INVITATION_FORBIDDEN,
                    }
                else:
                    # Other error status
                    order_item.team_channel_invitation_status = (
                        OrderItem.TEAM_CHANNEL_INVITATION_ERROR
                    )
                    order_item.save(update_fields=["team_channel_invitation_status"])

                    logger.error(
                        f"Error adding user {user.email} to team channel: status {participant_status}"
                    )
                    return {
                        "success": False,
                        "message": "Error al añadir al grupo",
                        "order_item_id": str(order_item.id),
                    }
            else:
                # No participant data in response
                order_item.team_channel_invitation_status = (
                    OrderItem.TEAM_CHANNEL_INVITATION_ERROR
                )
                order_item.save(update_fields=["team_channel_invitation_status"])

                logger.error(f"No participant data in response for user {user.email}")
                return {
                    "success": False,
                    "message": "Error al añadir al grupo",
                    "order_item_id": str(order_item.id),
                }
        else:
            # API call failed
            order_item.team_channel_invitation_status = (
                OrderItem.TEAM_CHANNEL_INVITATION_ERROR
            )
            order_item.save(update_fields=["team_channel_invitation_status"])

            error_message = response.get("message", "Error desconocido")
            logger.error(f"Evolution API call failed: {error_message}")
            return {
                "success": False,
                "message": "Error al añadir al grupo",
                "order_item_id": str(order_item.id),
            }

    except OrderItem.DoesNotExist:
        logger.error(f"Order item with ID {order_item_id} not found")
        raise Exception(f"Order item with ID {order_item_id} not found")
    except EvolutionAPIError as e:
        logger.error(
            f"Evolution API error while processing team channel invitation: {e}"
        )
        # Update status to error
        try:
            order_item = OrderItem.objects.get(id=order_item_id)
            order_item.team_channel_invitation_status = (
                OrderItem.TEAM_CHANNEL_INVITATION_ERROR
            )
            order_item.save(update_fields=["team_channel_invitation_status"])
        except:
            pass
        raise
    except Exception as e:
        logger.error(f"Error processing team channel invitation: {e}")
        # Update status to error
        try:
            order_item = OrderItem.objects.get(id=order_item_id)
            order_item.team_channel_invitation_status = (
                OrderItem.TEAM_CHANNEL_INVITATION_ERROR
            )
            order_item.save(update_fields=["team_channel_invitation_status"])
        except:
            pass
        raise


@shared_task(bind=True)
def process_team_channel_invitation_for_order(self, order_id):
    from django.db.models import Q

    try:
        order = Order.objects.get(oid=order_id)

        self.update_state(
            state="PROGRESS",
            meta={"message": "Procesando invitaciones a Team Channel"},
        )

        # Get items that need team channel invitations
        items_to_process = order.items.filter(deleted=False).filter(
            Q(team_channel_invitation_status__isnull=True)
            | Q(
                team_channel_invitation_status__in=[
                    OrderItem.TEAM_CHANNEL_INVITATION_PENDING,
                    OrderItem.TEAM_CHANNEL_INVITATION_ERROR,
                    OrderItem.TEAM_CHANNEL_INVITATION_FORBIDDEN,
                ]
            )
        )

        logger.info(f"Processing {items_to_process.count()} items")

        processed_items = []
        for item in items_to_process:
            # Only process items that have offerings with team_channel_id
            if item.offering.team_channel_id:
                task = process_team_channel_invitation.delay(str(item.id))
                processed_items.append(
                    {
                        "order_item_id": str(item.id),
                        "offering_name": item.offering.name,
                        "task_id": task.id,
                    }
                )
            else:
                logger.info(
                    f"Skipping item {item.id} - no team channel ID in offering {item.offering.name}"
                )

        logger.info(
            f"Processed {len(processed_items)} team channel invitations for order {order_id}"
        )

        return {
            "success": True,
            "message": f"Procesadas {len(processed_items)} invitaciones a Team Channel",
            "order_id": order_id,
            "processed_items": processed_items,
        }

    except Order.DoesNotExist:
        logger.error(f"Order with ID {order_id} not found")
        raise Exception(f"Order with ID {order_id} not found")
    except Exception as e:
        logger.error(
            f"Error processing team channel invitations for order {order_id}: {e}"
        )
        raise

import logging
from django.core.management.base import BaseCommand
from core.models import Order, OrderItem, StudentEnrollment

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Complete pending enrollments for sold offerings"

    def handle(self, *args, **options):
        # Logic to complete pending enrollments

        # Get OrderItems with sold orders and pending enrollments
        pending_items = OrderItem.objects.filter(
            order__stage=Order.SOLD_STAGE,
            enrollment=None,
        )
        logger.info(f"Found {pending_items.count()} pending enrollments to complete")

        for item in pending_items:
            try:
                # Ensure no duplicate enrollment exists
                if StudentEnrollment.objects.filter(order_item=item).exists():
                    logger.warning(
                        f"Enrollment already exists for OrderItem {item.id}, skipping"
                    )
                    continue
                # Create enrollment
                enrollment = StudentEnrollment.objects.create(
                    order_item=item,
                    user=item.order.owner,
                    is_active=True,
                )
                enrollment.created_at = item.created_at
                enrollment.save()
            except Exception as e:
                logger.error(f"Error creating enrollment for OrderItem {item.id}: {e}")
                raise e
        self.stdout.write(self.style.SUCCESS("✅ Completed pending enrollments"))

from celery.result import AsyncResult
from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication


class CeleryViewSet(viewsets.GenericViewSet):
    authentication_classes = [TokenAuthentication]
    permission_classes = [permissions.IsAuthenticated]

    @action(
        detail=False,
        methods=["GET"],
        url_path="task-status/(?P<task_id>[^/.]+)",
    )
    def check_task_status(self, request, task_id=None):
        task_result = AsyncResult(task_id)

        if task_result.successful():
            task_result.revoke(terminate=True)

        # Handle serialization of task info/details
        details = task_result.info
        if isinstance(details, Exception):
            # Convert exception to serializable format
            details = {
                "error_type": details.__class__.__name__,
                "error_message": str(details),
            }

        response_data = {
            "task_id": task_id,
            "status": task_result.status,
            "details": details,
        }

        return Response(response_data)

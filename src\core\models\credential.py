import uuid
import logging
from django.db import models
from django.db.models.signals import post_delete, pre_delete
from django.dispatch import receiver
from .base import AuditBaseModel


logger = logging.getLogger(__name__)


class Credential(AuditBaseModel):
    """
    Model representing a credential issued to a user.
    """

    cid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    issued_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Issued At",
    )
    has_expiration = models.BooleanField(
        default=False,
        verbose_name="Has Expiration",
    )
    expires_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="Expires At",
    )
    file = models.ForeignKey(
        "core.File",
        on_delete=models.SET_NULL,
        related_name="credentials",
        blank=True,
        null=True,
        verbose_name="Credential File",
    )

    class Meta:
        verbose_name = "Credential"
        verbose_name_plural = "Credentials"


@receiver(pre_delete, sender=Credential)
def handle_pre_delete(sender, instance, **kwargs):
    """Pre-delete signal handler for Credential model.

    This function can be used to perform any necessary cleanup or logging
    before a Credential instance is deleted.
    """
    try:
        enrollment = instance.enrollment
        if enrollment is not None:
            logger.info(
                f"Marking enrollment {enrollment.eid} as certificate not issued"
            )
            enrollment.certificate_issued = False
            enrollment.save()
    except Exception as e:
        # Avoid raising during delete signal handling; if needed, replace
        # with proper logging.
        logger.error(f"Error updating enrollment for credential {instance.cid}: {e}")


@receiver(post_delete, sender=Credential)
def handle_post_delete(sender, instance, **kwargs):
    """When a Credential is deleted, also delete its associated File object
        & mark enrollment as not issued.

    Notes:
    - If the File is shared by other Credentials, deleting the File will set
        their FK to NULL because the FK uses on_delete=models.SET_NULL.
    """
    try:
        file_obj = instance.file
        if file_obj is not None:
            # Delete the File instance; this will trigger the DB-level
            # on_delete behavior for other related objects.
            file_obj.delete()
    except Exception as e:
        # Avoid raising during delete signal handling; if needed, replace
        # with proper logging.
        logger.error(f"Error deleting file for credential {instance.cid}: {e}")

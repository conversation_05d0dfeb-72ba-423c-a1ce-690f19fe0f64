from rest_framework import serializers
from core.models import Credential, File


class WebsiteCredentialFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = File
        fields = [
            "fid",
            "url",
        ]


class WebsiteCredentialListSerializer(
    serializers.HyperlinkedModelSerializer,
):
    file = WebsiteCredentialFileSerializer()
    owner = serializers.SerializerMethodField()
    offering = serializers.SerializerMethodField()
    instructors = serializers.SerializerMethodField()
    hours = serializers.SerializerMethodField()

    def get_owner(self, obj):
        return (
            str(obj.enrollment.user) if obj.enrollment and obj.enrollment.user else None
        )

    def get_offering(self, obj):
        order_item = obj.enrollment.order_item.offering if obj.enrollment else None
        return (
            str(
                order_item.long_name
                if hasattr(order_item, "long_name") and order_item.long_name
                else order_item.name
            )
            if order_item
            else None
        )

    def get_instructors(self, obj):
        return (
            [
                str(instructor)
                for instructor in obj.enrollment.order_item.offering.instructors.all()
            ]
            if obj.enrollment
            and obj.enrollment.order_item
            and obj.enrollment.order_item.offering
            else []
        )

    def get_hours(self, obj):
        return (
            obj.enrollment.order_item.offering.hours
            if obj.enrollment
            and obj.enrollment.order_item
            and obj.enrollment.order_item.offering
            else None
        )

    class Meta:
        model = Credential
        fields = [
            "cid",
            "owner",
            "issued_at",
            "file",
            "offering",
            "instructors",
            "hours",
        ]

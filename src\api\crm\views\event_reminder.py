from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Q, Count, Case, When, IntegerField
from core.models import EventReminder, EventSchedule
from api.crm.serializers.event_reminder import (
    CrmEventReminderSerializer,
    CrmCreateEventReminderSerializer,
    CrmUpdateEventReminderSerializer,
    CrmEventReminderMetricsSerializer,
)
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.filters.event_reminder import CrmEventReminderFilter
from api.crm.tasks.event_invitations import (
    send_whatsapp_invitation,
    send_email_invitation,
    retry_failed_invitations,
)
from api.crm.utils.event_reminder import CrmEventReminderUtils


class CrmEventReminderViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    """
    ViewSet for managing event reminders with dual invitation types (WhatsApp and Email)
    """

    model_class = EventReminder
    queryset = (
        EventReminder.objects.filter(deleted=False)
        .select_related(
            "enrollment__event_schedule",
            "enrollment__user",
            "enrollment__event_schedule__whatsapp_template",
        )
        .order_by("-created_at")
    )
    serializer_class = CrmEventReminderSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]
    filterset_class = CrmEventReminderFilter
    lookup_field = "rid"

    swagger_tags = ["EventReminders"]

    def get_serializer_class(self):
        if self.action == "create":
            return CrmCreateEventReminderSerializer
        if self.action in ["update", "partial_update"]:
            return CrmUpdateEventReminderSerializer
        if self.action == "metrics":
            return CrmEventReminderMetricsSerializer
        return super().get_serializer_class()

    @action(detail=True, methods=["post"], url_path="send-whatsapp")
    def send_whatsapp_invitation(self, request, rid=None):
        """
        Send WhatsApp invitation for a specific reminder
        """
        reminder = self.get_object()

        if not (
            reminder.enrollment
            and reminder.enrollment.event_schedule
            and reminder.enrollment.event_schedule.whatsapp_template
        ):
            return Response(
                {"detail": "No WhatsApp template configured for this event schedule"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not reminder.enrollment.event_schedule.is_whatsapp_active:
            return Response(
                {
                    "detail": "WhatsApp notifications are disabled for this event schedule"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if reminder.status_whatsapp not in [
            EventReminder.PENDING,
            EventReminder.FAILED,
        ]:
            return Response(
                {
                    "detail": f"WhatsApp invitation already {reminder.status_whatsapp.lower()}"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Schedule the task
        send_whatsapp_invitation.delay(str(reminder.rid))

        return Response(
            {"detail": "WhatsApp invitation scheduled for sending"},
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"], url_path="send-email")
    def send_email_invitation(self, request, rid=None):
        """
        Send email invitation for a specific reminder
        """
        reminder = self.get_object()

        # Basic validation - enrollment and event_schedule must exist
        if not (reminder.enrollment and reminder.enrollment.event_schedule):
            return Response(
                {"detail": "No event schedule configured for this reminder"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        event_schedule = reminder.enrollment.event_schedule

        # If emails are not automatic, then scheduled_datetime_email is required
        if (
            not event_schedule.emails_reminder_auto
            and not event_schedule.scheduled_datetime_email
        ):
            return Response(
                {"detail": "No email scheduling configured for this event schedule"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if reminder.status_email not in [EventReminder.PENDING, EventReminder.FAILED]:
            return Response(
                {"detail": f"Email invitation already {reminder.status_email.lower()}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Schedule the task
        send_email_invitation.delay(str(reminder.rid))

        return Response(
            {"detail": "Email invitation scheduled for sending"},
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"], url_path="retry-whatsapp")
    def retry_whatsapp_invitation(self, request, rid=None):
        """
        Retry failed WhatsApp invitation for a specific reminder
        """
        reminder = self.get_object()

        if not (
            reminder.enrollment
            and reminder.enrollment.event_schedule
            and reminder.enrollment.event_schedule.whatsapp_template
        ):
            return Response(
                {
                    "detail": "No se ha configurado una plantilla WhatsApp para este evento"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not reminder.enrollment.event_schedule.is_whatsapp_active:
            return Response(
                {
                    "detail": "Las notificaciones WhatsApp están deshabilitadas para este evento"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if reminder.status_whatsapp != EventReminder.FAILED:
            return Response(
                {"detail": "WhatsApp invitation is not in failed status"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if reminder.retry_count_whatsapp >= 3:
            return Response(
                {
                    "detail": "Se ha alcanzado el máximo número de reintentos para la invitación WhatsApp"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Reset status and schedule retry
        reminder.status_whatsapp = EventReminder.PENDING
        reminder.save(update_fields=["status_whatsapp"])

        send_whatsapp_invitation.delay(str(reminder.rid))

        return Response(
            {"detail": "WhatsApp invitation retry scheduled"},
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["post"], url_path="retry-email")
    def retry_email_invitation(self, request, rid=None):
        """
        Retry failed email invitation for a specific reminder
        """
        reminder = self.get_object()

        if reminder.status_email != EventReminder.FAILED:
            return Response(
                {"detail": "Email invitation is not in failed status"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if reminder.retry_count_email >= 3:
            return Response(
                {"detail": "Maximum retry attempts reached for email invitation"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Reset status and schedule retry
        reminder.status_email = EventReminder.PENDING
        reminder.save(update_fields=["status_email"])

        send_email_invitation.delay(str(reminder.rid))

        return Response(
            {"detail": "Email invitation retry scheduled"},
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["post"], url_path="bulk-retry")
    def bulk_retry_failed_invitations(self, request):
        """
        Bulk retry failed invitations with optional filters
        """
        event_schedule_id = request.data.get("event_schedule_id")
        invitation_type = request.data.get(
            "invitation_type"
        )  # 'whatsapp', 'email', or None for both

        # Validate invitation_type
        if invitation_type and invitation_type not in ["whatsapp", "email"]:
            return Response(
                {"detail": "invitation_type must be 'whatsapp' or 'email'"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Schedule the bulk retry task
        retry_failed_invitations.delay(event_schedule_id, invitation_type)

        return Response(
            {"detail": "Bulk retry operation scheduled"},
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"], url_path="metrics")
    def get_sending_metrics(self, request):
        """
        Get sending metrics for event reminders
        Returns counts by status for both WhatsApp and Email invitations
        """
        # Apply the same filters that would be used in the list view
        queryset = self.filter_queryset(self.get_queryset())
        metrics_data = CrmEventReminderUtils.calculate_metrics(queryset)
        serializer = CrmEventReminderMetricsSerializer(metrics_data)

        return Response(
            serializer.data,
            status=status.HTTP_200_OK,
        )

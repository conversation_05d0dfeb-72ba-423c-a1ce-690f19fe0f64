from api.crm.services.tokechat.api_request import fetcher

"""
Retrieve a list of sequences

Response body:
[
  {
    "id": 499209,
    "name": "Seguimiento cuotas"
  }
]
"""


def get_sequences():
    try:
        sequences = fetcher(url="/sequences/")
        if sequences.get("success"):
            return sequences.get("response")

        return None
    except Exception as e:
        print(f"An error occurred: {e}")
        return None


def get_sequence_by_name(name: str):
    try:
        sequences = get_sequences()
        if len(sequences) > 0:
            return next(
                (item for item in sequences if item["name"] == name),
                None,
            )

        return None
    except Exception as e:
        print(f"An error occurred: {e}")
        return None

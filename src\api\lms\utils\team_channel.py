import logging
from api.crm.services.evolution_api import EvolutionAPIClient
from services.cache.redis import CacheManager

logger = logging.getLogger(__name__)

# Use this constant to fetch the team channels from the cache in other functions
TEAM_CHANNELS_PREFIX = "ceu_team_channels"
TEAM_CHANNELS_CACHE_KEY = "all"


def fetch_team_channels(client: EvolutionAPIClient):
    """
    Handles team channel fetching and caching
    """
    try:
        # Initialize cache manager with the same prefix used by ViewSet
        cache_manager = CacheManager(TEAM_CHANNELS_PREFIX)

        logger.info(
            f"Fetching all groups from Evolution API instance: {client.instance_name}"
        )
        response = client.groups.fetch_all()

        # Map team channels response to use in serializer (same mapping as Celery task)
        mapping = {
            "id": "id",
            "subject": "subject",
            "subjectTime": "subject_time",
            "desc": "description",
            "pictureUrl": "picture_url",
            "size": "size",
            "creation": "creation",
        }

        response_data = response.get("data", [])
        mapped_response = [
            {mapping.get(key, key): value for key, value in group.items()}
            for group in response_data
            if group.get("subject", "") != ""
        ]

        # Cache the results temporarily (will be overwritten by Celery task)
        cache_manager.set(
            TEAM_CHANNELS_CACHE_KEY,
            mapped_response,
            timeout=60 * 3,  # 3 minutes temporary cache
        )

        logger.info(
            f"Fallback API call successful, cached {len(mapped_response)} team channels"
        )
        return mapped_response

    except Exception as e:
        logger.error(f"Fallback API call failed: {e}")
        # Return empty list if both cache and API fail
        return None

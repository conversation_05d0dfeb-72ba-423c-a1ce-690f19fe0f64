from django.forms.utils import ValidationError
from rest_framework import serializers
from core.models import Template
from api.shared.serializers.file import FileSerializer
from api.crm.utils.template import TemplateParser
import requests
from django.conf import settings
from api.utils import (
    perform_create_image_file,
)
from core.constants import TEMPLATE_HEADER_IMAGE_HEIGHT, TEMPLATE_HEADER_IMAGE_WIDTH
import json
from api.crm.serializers.template_type import CrmTemplateTypeSerializer


class CrmTemplateSerializer(serializers.ModelSerializer):
    header_image = FileSerializer(read_only=True, allow_null=True)

    class Meta:
        model = Template
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]


class CrmRetrieveTemplateSerializer(serializers.ModelSerializer):
    header_image = FileSerializer(read_only=True, allow_null=True)
    type = serializers.SerializerMethodField()
    parsed_body_text = serializers.SerializerMethodField()

    class Meta:
        model = Template
        exclude = [
            "deleted",
            "deleted_at",
            "deleted_by",
        ]

    def get_type(self, obj):
        return CrmTemplateTypeSerializer(obj.type).data

    def get_parsed_body_text(self, obj):
        """
        Return a preview of the template with example values
        """
        if not obj.body_text or not obj.type:
            return obj.body_text

        try:
            parser = TemplateParser()
            return parser.preview_template(obj.body_text, obj.type)
        except Exception:
            return obj.body_text


class CrmCreateTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Template
        fields = ["tid", "name", "type"]
        read_only_fields = ["tid"]


class CrmUpdateTemplateSerializer(CrmTemplateSerializer):
    header_image_file = serializers.FileField(write_only=True, required=False)
    delete_header_image = serializers.BooleanField(write_only=True, required=False)

    def update(self, instance, validated_data):
        header_image_file = validated_data.pop("header_image_file", None)
        delete_header_image = validated_data.pop("delete_header_image", False)

        template = super().update(instance, validated_data)

        if delete_header_image:
            self.__perform_delete_header_image(template)

        if header_image_file:
            if template.header_image:
                self.__perform_delete_header_image(template)

            header_image = perform_create_image_file(
                header_image_file,
                TEMPLATE_HEADER_IMAGE_WIDTH,
                TEMPLATE_HEADER_IMAGE_HEIGHT,
                output_format="JPEG",
            )
            template.header_image = header_image

        template.save()
        return template

    def __perform_delete_header_image(self, template):
        if template.header_image:
            template.header_image.delete()
            template.header_image = None


class CrmSendTemplateSerializer(CrmTemplateSerializer):
    def send_to_meta(self, template: Template):
        """
        Envía la plantilla a Meta y actualiza su estado a IN_REVIEW.
        """
        if not template.body_text:
            raise ValidationError("El cuerpo de la plantilla no puede estar vacío.")

        header_file = template.header_image
        header_handle = self._upload_header_image(header_file) if header_file else None

        # Construcción del cuerpo del request
        post_body = {
            "name": template.name.lower().replace(" ", "_"),
            "category": "MARKETING",
            "allow_category_change": False,
            "language": "es",
            "components": [
                {"type": "BODY", "text": template.body_text.replace("\r", "")}
            ],
        }

        # Si hay parámetros posicionales, agregarlos al request
        if template.positional_params_example:
            post_body["components"][0]["example"] = {
                "body_text": [template.positional_params_example]
            }

        # si hay buttons agregarlos al request
        if template.buttons:
            post_body["components"].append(
                {"type": "BUTTONS", "buttons": self._format_buttons(template.buttons)}
            )
        # Agregar el HEADER si existe
        if header_handle:
            post_body["components"].insert(
                0,
                {
                    "type": "HEADER",
                    "format": "IMAGE",
                    "example": {"header_handle": [header_handle]},
                },
            )

        # Enviar la plantilla a Meta
        url = f"https://graph.facebook.com/v21.0/{settings.WHATSAPP_BUSINESS_ACCOUNT_ID}/message_templates"
        headers = {
            "Authorization": f"Bearer {settings.META_ACCESS_TOKEN}",
            "Content-Type": "application/json",
        }

        response = requests.post(url, json=post_body, headers=headers)
        if response.status_code != 200:
            raise ValidationError(
                f"{response.json().get('error', {}).get('error_user_msg', 'Error desconocido')}"
            )

        # Actualizar el estado de la plantilla
        template.status = Template.IN_REVIEW
        template.save()

    def _upload_header_image(self, header_file):
        """
        Sube la imagen de encabezado a Meta y devuelve el identificador del archivo.
        """
        # Iniciar sesión de subida
        url = f"https://graph.facebook.com/v21.0/{settings.META_APP_ID}/uploads"
        headers = {"Authorization": f"OAuth {settings.META_ACCESS_TOKEN}"}

        # Use the properties from the File model
        file_metadata = {
            "file_name": header_file.name,
            "file_length": header_file.size,
            "file_type": header_file.content_type,
        }

        response = requests.post(url, params=file_metadata, headers=headers)
        if response.status_code != 200:
            raise ValidationError(
                f"Error al iniciar la subida de la imagen: {response.json().get('error', {}).get('message', 'Error desconocido')}"
            )
        upload_session_id = response.json().get("id")  # upload:<id>
        upload_session_id = upload_session_id.split(":")[1]

        # Subir el archivo
        upload_url = f"https://graph.facebook.com/v21.0/upload:{upload_session_id}"

        try:
            # Ensure you're using the full, absolute URL
            full_url = f"{settings.MINIO_INTERNAL_BASE_URL}/{header_file.bucket_name}/{header_file.object_name}"

            response = requests.get(full_url)
            response.raise_for_status()  # Raise an exception for bad responses
            file_data = response.content
        except requests.RequestException as e:
            raise ValidationError(f"Error fetching file: {str(e)}")

        headers.update({"file_offset": "0"})
        response = requests.post(upload_url, headers=headers, data=file_data)
        if response.status_code != 200:
            raise ValidationError(
                f"Error al subir la imagen: {response.json().get('error', {}).get('message', 'Error desconocido')}"
            )

        return response.json().get("h")

    def _format_buttons(self, template_buttons):
        if not template_buttons:
            return None
        # Si ya es una lista, lo convierte a lista, si es un string, lo parsea
        buttons = (
            json.loads(template_buttons)
            if isinstance(template_buttons, str)
            else template_buttons
        )

        processed_buttons = []
        for button in buttons:
            processed_button = {
                "type": button["type"],
                "text": button["text"],
                "url": button["url"],
                # "example": button.get("example", [])
            }
            # si hay example, agregarlo al request
            if (
                button.get("example")
                and isinstance(button.get("example"), list)
                and len(button.get("example")) > 0
            ):
                processed_button["example"] = button.get("example")
            processed_buttons.append(processed_button)

        return processed_buttons


class CrmFetchTemplateStatusSerializer(serializers.ModelSerializer):
    def fetch_meta_template_status(self, template: Template):
        """
        Fetch template status from Meta and update local database
        """
        # Skip templates that haven't been sent to Meta
        if template.status not in [
            Template.IN_REVIEW,
            Template.APPROVED,
            Template.PAUSED,
            Template.DISABLED,
            Template.REJECTED,
        ]:
            return template.status

        url = f"https://graph.facebook.com/v21.0/{settings.WHATSAPP_BUSINESS_ACCOUNT_ID}/message_templates"
        headers = {"Authorization": f"Bearer {settings.META_ACCESS_TOKEN}"}
        params = {"name": template.name.lower().replace(" ", "_")}

        try:
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()

            data = response.json().get("data", [])
            if not data:
                # If no template found, it might not have been processed by Meta yet
                return template.status

            meta_status = data[0].get("status")

            # Map Meta status to local status
            status_mapping = {
                "APPROVED": Template.APPROVED,
                "PENDING": Template.IN_REVIEW,
                "IN_REVIEW": Template.IN_REVIEW,
                "PAUSED": Template.PAUSED,
                "DISABLED": Template.DISABLED,
                "REJECTED": Template.REJECTED,
            }

            new_status = status_mapping.get(meta_status, Template.DRAFT)

            if template.status != new_status:
                template.status = new_status
                template.save()

            if template.header_image:
                for component in data[0].get("components", []):
                    if (
                        component.get("type") == "HEADER"
                        and component.get("format") == "IMAGE"
                    ):
                        header_handle = component.get("example", {}).get(
                            "header_handle", [None]
                        )[0]
                        if template.header_image_meta_url != header_handle:
                            print(
                                f"Updating header_image_meta_url for template {template.name} from {template.header_image_meta_url} to {header_handle}"
                            )
                            template.header_image_meta_url = header_handle
                            template.save()

            return new_status

        except requests.RequestException:
            # If there's an error fetching status, return current status
            return template.status


class CrmSendPreviewTemplateSerializer(serializers.ModelSerializer):
    phone_number = serializers.CharField(
        write_only=True, help_text="Número de WhatsApp a enviar la prueba"
    )

    class Meta:
        model = Template
        fields = ["phone_number"]


class CrmBulkDeleteTemplateSerializer(serializers.Serializer):
    tids = serializers.ListField(child=serializers.UUIDField())

    class Meta:
        fields = ["tids"]
        read_only_fields = ["tids"]

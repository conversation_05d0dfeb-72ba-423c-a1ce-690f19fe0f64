from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import TokenAuthentication
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>arser
from drf_yasg.utils import swagger_auto_schema
from core.models import User, File
from api.classroom.serializers.auth import ProfileSerializer, UpdateProfileSerializer
from api.classroom.services.file import upload_file_to_minio
from rest_framework.exceptions import NotFound
from uuid import UUID


class UserViewSet(viewsets.ViewSet):
    authentication_classes = [TokenAuthentication]

    permission_classes = [IsAuthenticated]
    parser_classes = [<PERSON><PERSON><PERSON><PERSON><PERSON>, MultiPartPars<PERSON>, FormParser]
    lookup_field = "uid"

    def get_object(self, uid):
        try:
            return User.objects.get(uid=uid, deleted=False)
        except User.DoesNotExist:
            raise NotFound("Usuario no encontrado.")

    @swagger_auto_schema(responses={status.HTTP_200_OK: ProfileSerializer()})
    def retrieve(self, request, uid=None):
        user = self.get_object(uid)
        serializer = ProfileSerializer(user)
        return Response(serializer.data)

    @swagger_auto_schema(
        request_body=UpdateProfileSerializer,
        responses={status.HTTP_200_OK: "Perfil actualizado correctamente."},
    )
    def partial_update(self, request, uid=None):
        user = self.get_object(uid)
        serializer = UpdateProfileSerializer(
            instance=user, data=request.data, partial=True, context={"request": request}
        )
        if serializer.is_valid():
            serializer.save()
            return Response({"message": "Perfil actualizado correctamente."})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(
        methods=["POST"],
        detail=True,
        url_path="upload-profile-photo",
    )
    def upload_profile_photo(self, request, uid=None):
        user = self.get_object(uid)

        if "file" not in request.FILES:
            return Response(
                {"error": "No se ha proporcionado ningún archivo"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Eliminar la foto actual si existe
        if user.profile_photo:
            user.profile_photo.delete()
            user.profile_photo = None
            user.save()

        file_obj = request.FILES["file"]
        fid, object_name = upload_file_to_minio(file_obj)

        file = File.objects.create(
            fid=fid,
            is_used=False,
            is_private=True,
            name=object_name.split("/")[-1],
            bucket_name="private",
            object_name=object_name,
        )

        user.profile_photo = file
        user.save()

        file.is_used = True
        file.save()

        return Response(
            {"message": "Foto de perfil actualizada correctamente."},
            status=status.HTTP_200_OK,
        )

    @action(
        methods=["DELETE"],
        detail=True,
        url_path="delete-profile-photo/(?P<fid>[^/.]+)",
    )
    def delete_profile_photo(self, request, uid=None, fid=None):
        user = self.get_object(uid)
        try:
            fid_uuid = UUID(str(fid))
        except (ValueError, TypeError):
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={"detail": "Formato de ID inválido."},
            )

        if not user.profile_photo or str(user.profile_photo.fid) != str(fid_uuid):
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={
                    "detail": "La foto de perfil no coincide con el archivo enviado."
                },
            )

        user.profile_photo.delete()
        user.profile_photo = None
        user.save()

        return Response(
            status=status.HTTP_204_NO_CONTENT,
            data={"detail": "Foto de perfil eliminada correctamente."},
        )

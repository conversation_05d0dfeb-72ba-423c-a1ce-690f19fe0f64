from django.contrib import admin
from core.models import EventReminder


@admin.register(EventReminder)
class EventReminderAdmin(admin.ModelAdmin):
    list_display = [
        "rid",
        "get_enrollment_phone",
        "get_whatsapp_template",
        "get_scheduled_datetime_whatsapp",
        "get_scheduled_datetime_email",
        "status_whatsapp",
        "status_email",
        "sent_at_whatsapp",
        "sent_at_email",
        "last_error_whatsapp",
        "last_error_email",
        "retry_count_whatsapp",
        "retry_count_email",
        "created_at",
        "updated_at",
    ]

    list_filter = [
        "enrollment__event_schedule__stage",
        "status_whatsapp",
        "status_email",
    ]

    search_fields = [
        "rid",
        "enrollment__event_schedule__name",
        "enrollment__user__email",
        "enrollment__phone_number",
        "enrollment__event_schedule__whatsapp_template__name",
    ]

    def get_enrollment_phone(self, obj):
        """Show phone number from enrollment"""
        if obj.enrollment and obj.enrollment.phone_number:
            return obj.enrollment.phone_number
        return "-"

    def get_whatsapp_template(self, obj):
        """Get WhatsApp template from event schedule"""
        if (
            obj.enrollment
            and obj.enrollment.event_schedule
            and obj.enrollment.event_schedule.whatsapp_template
        ):
            return obj.enrollment.event_schedule.whatsapp_template.name
        return "-"

    get_whatsapp_template.short_description = "WhatsApp Template"

    def get_scheduled_datetime_whatsapp(self, obj):
        """Get WhatsApp scheduled datetime from event schedule"""
        if obj.enrollment and obj.enrollment.event_schedule:
            return obj.enrollment.event_schedule.scheduled_datetime_whatsapp
        return "-"

    get_scheduled_datetime_whatsapp.short_description = "WhatsApp Scheduled"

    def get_scheduled_datetime_email(self, obj):
        """Get email scheduled datetime from event schedule"""
        if obj.enrollment and obj.enrollment.event_schedule:
            return obj.enrollment.event_schedule.scheduled_datetime_email
        return "-"

    get_scheduled_datetime_email.short_description = "Email Scheduled"

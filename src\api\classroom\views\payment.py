from django.contrib.auth import get_user_model
from rest_framework import viewsets, status, permissions
from rest_framework.authentication import TokenAuthentication
from rest_framework.response import Response
from rest_framework.exceptions import NotFound, PermissionDenied
from django.utils import timezone
from core.models import Payment
from api.classroom.serializers.payment import FullPaymentSummarySerializer

User = get_user_model()


class ClassroomPaymentViewSet(viewsets.ViewSet):
    """
    Endpoint para mostrar historial de pagos y resumen financiero
    del estudiante en Classroom.
    """

    authentication_classes = [TokenAuthentication]
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = "uid"

    def retrieve(self, request, uid=None):
        """
        Returns the payment history and financial summary of the authenticated user.

        Este endpoint permite consultar el historial completo de pagos junto al resumen
        financiero del usuario identificado por su uid (pk). El usuario autenticado solo
        puede acceder a su propia información.

        - **payments**: Lista de pagos realizados o pendientes.
        - **summary**: Resumen financiero con el total pagado, total pendiente y próxima fecha de pago.

        Requiere autenticación.
        """
        if str(request.user.uid) != str(uid):
            raise PermissionDenied("No tienes permiso para acceder a esta información.")

        try:
            user = User.objects.get(uid=uid)
        except User.DoesNotExist:
            raise NotFound("Usuario no encontrado")

        payments = Payment.objects.filter(
            order__owner=user,
            is_refund=False,
            deleted=False,
        ).order_by("-payment_date", "-scheduled_payment_date")

        total_paid = sum(p.amount for p in payments if p.is_paid and not p.is_lost)
        pending_payments = sum(
            p.amount for p in payments if not p.is_paid and not p.is_lost
        )
        next_payment = (
            payments.filter(
                is_paid=False,
                is_lost=False,
                scheduled_payment_date__gte=timezone.now(),
            )
            .order_by("scheduled_payment_date")
            .first()
        )

        data = {
            "payments": payments,
            "total_paid": total_paid,
            "pending_payments": pending_payments,
            "next_payment_date": (
                next_payment.scheduled_payment_date if next_payment else None
            ),
        }

        serializer = FullPaymentSummarySerializer(data)
        return Response(serializer.data, status=status.HTTP_200_OK)

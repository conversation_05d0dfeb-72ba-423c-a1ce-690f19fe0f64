from rest_framework import viewsets
from core.models import Term
from api.classroom.serializers.auth import TermSelectSerializer
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import TokenAuthentication


class TermViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Term.objects.filter(deleted=False)
    serializer_class = TermSelectSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

import logging
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import viewsets, status
from django.db import transaction
from core.models import (
    EventSchedule,
    Event,
    Major,
    Order,
    OrderItem,
)
from api.mixins import SwaggerTagMixin
from api.automations.serializers.event import (
    AutomationsEventEnrollmentSerializer,
)
from api.website.serializers.event import (
    WebsiteEventRetrieveSerializer,
    WebsiteEventListSerializer,
    WebsiteEventMajorSerializer,
)
from api.paginations import StandardResultsPagination
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.permissions import IsStaffUser

logger = logging.getLogger(__name__)


class AutomationsEventViewSet(
    SwaggerTagMixin,
    viewsets.ReadOnlyModelViewSet,
):
    model_class = EventSchedule
    serializer_class = WebsiteEventRetrieveSerializer
    queryset = EventSchedule.objects.filter(deleted=False).order_by("-created_at")
    pagination_class = StandardResultsPagination

    lookup_field = "esid"

    swagger_tags = ["Automations Event"]

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]

    def get_object(self):
        """
        Returns the object the view is displaying.
        Supports lookup by both 'esid' and 'short_esid' fields.
        Automatically determines which field to use based on the lookup value format.
        """
        import uuid

        queryset = self.filter_queryset(self.get_queryset())

        # Get the lookup value from URL
        lookup_value = self.kwargs[self.lookup_url_kwarg or self.lookup_field]

        # Check if lookup_value is a valid UUID format
        is_uuid = False
        try:
            uuid.UUID(lookup_value)
            is_uuid = True
        except ValueError:
            is_uuid = False

        if is_uuid:
            # If it's a UUID, try esid first, then short_esid as fallback
            try:
                obj = queryset.get(esid=lookup_value)
                self.check_object_permissions(self.request, obj)
                return obj
            except EventSchedule.DoesNotExist:
                pass

            # Fallback to short_esid if esid didn't work
            try:
                obj = queryset.get(short_esid=lookup_value)
                self.check_object_permissions(self.request, obj)
                return obj
            except EventSchedule.DoesNotExist:
                pass
        else:
            # If it's not a UUID, only try short_esid
            try:
                obj = queryset.get(short_esid=lookup_value)
                self.check_object_permissions(self.request, obj)
                return obj
            except EventSchedule.DoesNotExist:
                pass

        # If not found by either method, raise 404
        from django.http import Http404

        raise Http404("No EventSchedule matches the given query.")

    def get_serializer(self, *args, **kwargs):
        if self.action == "list":
            self.serializer_class = WebsiteEventListSerializer
        return super().get_serializer(*args, **kwargs)

    def get_queryset(self):
        # Return only if the event has EventSchedules is general and not be null
        # also it's on stage Launched or Enrollment closed
        return self.queryset.filter(
            # is_general=True,
            stage__in=[
                Event.LAUNCHED_STAGE,
            ],
        ).distinct()

    @action(
        detail=True,
        methods=["POST"],
        url_path="enroll",
        url_name="enroll",
        serializer_class=AutomationsEventEnrollmentSerializer,
    )
    def enroll(self, request, pk=None, *args, **kwargs):
        """
        Enroll in the event schedule and create an order if successful.
        """
        event_schedule = self.get_object()

        # Check if enrollment is available for this event schedule
        if event_schedule.stage in [
            Event.PLANNING_STAGE,
            Event.ENROLLMENT_CLOSED_STAGE,
            Event.FINISHED_STAGE,
        ]:
            return Response(
                {"message": "Enrollment is not available for this event schedule."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Captcha validation
        # Skip captcha validation for automations
        data = request.data.copy()
        # Always use the actual esid from the found event_schedule object
        data["event_schedule"] = event_schedule.esid

        serializer = AutomationsEventEnrollmentSerializer(
            data=data,
            context={
                "event_schedule": event_schedule,
            },
        )

        if serializer.is_valid():
            try:
                with transaction.atomic():
                    # Save the enrollment
                    enrollment = serializer.save()

                    # Process enrollment result and determine response
                    enrollment_result = self._process_enrollment_and_order(
                        event_schedule, enrollment
                    )

                    return Response(
                        enrollment_result["data"], status=enrollment_result["status"]
                    )

            except Exception as e:
                logger.error(f"Enrollment transaction failed: {str(e)}")
                return Response(
                    {"message": "Enrollment failed", "error": str(e)},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def _process_enrollment_and_order(self, event_schedule, enrollment):
        """
        Process enrollment and handle order creation with lead validation.

        Returns:
            dict: Contains response data and status code
        """
        # Base response data
        response_data = {
            "message": "Enrollment successful",
            "enrollment_id": str(enrollment.id),
        }

        # Check if we should create an order
        should_create_order = self._should_create_order(event_schedule, enrollment)

        if should_create_order:
            # Check if user is already a lead for this offering
            is_already_lead = self._check_existing_lead(
                enrollment.user, event_schedule.event.offering
            )

            if is_already_lead:
                # Mark enrollment as already_lead and don't create order
                enrollment.already_lead = True
                enrollment.save(update_fields=["already_lead"])

                response_data.update(
                    {
                        "message": (
                            "Enrollment successful - "
                            "User is already a lead for this offering"
                        ),
                        "already_lead": True,
                        "note": (
                            "No new order created - "
                            "user already has an order for this offering"
                        ),
                    }
                )
            else:
                # Attempt to create order
                order_result = self._create_order_for_enrollment(
                    enrollment, event_schedule.event.offering
                )

                if order_result["success"]:
                    response_data.update(
                        {
                            "message": (
                                "Enrollment and order created successfully"
                                if not enrollment.needs_conciliation
                                else "Enrollment created but needs conciliation"
                            ),
                            "order_id": str(order_result["order"].oid),
                        }
                    )
                else:
                    # Order creation failed, but enrollment was successful
                    response_data.update(
                        {
                            "message": (
                                "Enrollment successful, but order creation failed"
                            ),
                            "error": "Order could not be created",
                            "error_details": order_result["error"],
                        }
                    )
        else:
            # Determine why no order was created
            if not event_schedule.event.offering:
                response_data["note"] = "No offering associated with this event"
            elif not enrollment.user:
                response_data["note"] = "No order created - user not specified"
            elif not enrollment.has_contact:
                response_data["note"] = "No order created - contact verification needed"

        # Add conciliation status if needed
        if enrollment.needs_conciliation:
            response_data["needs_conciliation"] = True
            if "message" in response_data and not response_data["message"].endswith(
                "conciliation"
            ):
                response_data["message"] = (
                    f"{response_data['message']} but needs conciliation"
                )

        return {"data": response_data, "status": status.HTTP_201_CREATED}

    def _should_create_order(self, event_schedule, enrollment):
        """
        Determine if an order should be created for the enrollment.

        Args:
            event_schedule: The event schedule instance
            enrollment: The enrollment instance

        Returns:
            bool: True if order should be created, False otherwise
        """
        return (
            event_schedule.event.offering is not None
            and enrollment.user is not None
            and enrollment.has_contact
        )

    def _check_existing_lead(self, user, offering):
        """
        Check if user already has an order for the given offering.

        Args:
            user: The user instance
            offering: The offering instance

        Returns:
            bool: True if user already has an order for this offering, False otherwise
        """
        if not user or not offering:
            return False

        # Check if user has any existing orders with this offering
        existing_orders = Order.objects.filter(
            owner=user, items__offering=offering, deleted=False
        ).exists()

        return existing_orders

    def _create_order_for_enrollment(self, enrollment, offering):
        """
        Create an order for the enrollment.

        Args:
            enrollment: The enrollment instance
            offering: The offering instance

        Returns:
            dict: Contains success status, order (if created), and error details
        """
        try:
            # Create order in prospect stage
            order = Order.objects.create(
                owner=enrollment.user,
                stage=Order.PROSPECT_STAGE,
                is_international=False,
                has_full_scholarship=False,
            )

            # Create order item for the offering
            OrderItem.objects.create(
                order=order,
                offering=offering,
                quantity=1,
            )

            logger.info(
                f"Order {order.oid} created successfully for enrollment {enrollment.id}"
            )

            return {"success": True, "order": order, "error": None}

        except Exception as order_error:
            # Log the specific error for debugging
            error_msg = (
                f"Error creating order for enrollment {enrollment.id}: "
                f"{str(order_error)}"
            )
            logger.error(error_msg, exc_info=True)

            return {"success": False, "order": None, "error": str(order_error)}

    @action(
        detail=False,
        methods=["GET"],
        url_path="majors",
        url_name="majors",
    )
    def get_majors(self, request, *args, **kwargs):
        """
        Get all majors from the event schedules.
        """
        majors = Major.objects.filter(
            deleted=False,
        )
        return Response(
            {"data": WebsiteEventMajorSerializer(majors, many=True).data},
            status=status.HTTP_200_OK,
        )

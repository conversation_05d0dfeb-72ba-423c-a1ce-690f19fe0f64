from rest_framework import serializers
from core.models import Credential
from api.lms.serializers.file import LmsFileSerializer
from django.utils import timezone


class LmsBaseCredentialSerializer(serializers.ModelSerializer):
    """
    Base serializer for Credential model with common fields.
    """

    key = serializers.CharField(source="cid", read_only=True)

    class Meta:
        model = Credential
        fields = [
            "cid",
            "key",
            "issued_at",
            "has_expiration",
            "expires_at",
            "created_at",
            "updated_at",
        ]
        extra_kwargs = {
            "cid": {"read_only": True},
            "issued_at": {"read_only": True},
            "created_at": {"read_only": True},
            "updated_at": {"read_only": True},
        }


class LmsCredentialListSerializer(LmsBaseCredentialSerializer):
    """
    Serializer for listing credentials.
    """

    file = LmsFileSerializer(read_only=True)
    verification_url = serializers.SerializerMethodField()

    def get_verification_url(self, obj):
        from django.conf import settings

        return f"{settings.APP_HOST}/credencial/{obj.cid}"

    class Meta(LmsBaseCredentialSerializer.Meta):
        fields = LmsBaseCredentialSerializer.Meta.fields + [
            "file",
            "verification_url",
        ]


class LmsCredentialRetrieveSerializer(LmsBaseCredentialSerializer):
    """
    Serializer for retrieving detailed credential information.
    """

    file = LmsFileSerializer(read_only=True)
    created_by = serializers.CharField(
        source="created_by.get_full_name", read_only=True
    )
    updated_by = serializers.CharField(
        source="updated_by.get_full_name", read_only=True
    )

    class Meta(LmsBaseCredentialSerializer.Meta):
        fields = LmsBaseCredentialSerializer.Meta.fields + [
            "file",
            "created_by",
            "updated_by",
        ]


class LmsCredentialCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating new credentials.
    """

    has_expiration = serializers.BooleanField(default=False)
    expires_at = serializers.DateTimeField(required=False, allow_null=True)
    file = serializers.UUIDField(required=False, allow_null=True, write_only=True)

    class Meta:
        model = Credential
        fields = [
            "has_expiration",
            "expires_at",
            "file",
        ]

    def validate(self, attrs):
        """
        Validate that expires_at is provided when has_expiration is True.
        """
        has_expiration = attrs.get("has_expiration", False)
        expires_at = attrs.get("expires_at")

        if has_expiration and not expires_at:
            raise serializers.ValidationError(
                "expires_at is required when has_expiration is True."
            )

        if not has_expiration:
            attrs["expires_at"] = None

        return attrs

    def validate_file(self, value):
        """
        Validate that the file exists if provided.
        """
        if value:
            from core.models import File

            try:
                file_obj = File.objects.get(fid=value)
                return file_obj
            except File.DoesNotExist:
                raise serializers.ValidationError("The specified file does not exist.")
        return value

    def create(self, validated_data):
        """
        Create a new credential instance.
        """
        return super().create(validated_data)


class LmsCredentialUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating existing credentials.
    """

    has_expiration = serializers.BooleanField(
        required=False,
    )
    expires_at = serializers.DateTimeField(
        required=False,
        allow_null=True,
    )
    file = serializers.UUIDField(
        required=False,
        allow_null=True,
        write_only=True,
    )
    issued_at = serializers.DateTimeField(
        required=False,
        allow_null=True,
    )

    class Meta:
        model = Credential
        fields = [
            "has_expiration",
            "expires_at",
            "issued_at",
            "file",
        ]

    def validate(self, attrs):
        """
        Validate that expires_at is provided when has_expiration is True.
        """
        instance = self.instance
        has_expiration = attrs.get(
            "has_expiration", instance.has_expiration if instance else False
        )
        expires_at = attrs.get("expires_at", instance.expires_at if instance else None)

        if has_expiration and not expires_at:
            raise serializers.ValidationError(
                "expires_at is required when has_expiration is True."
            )

        if not has_expiration:
            attrs["expires_at"] = None

        return attrs

    def validate_file(self, value):
        """
        Validate that the file exists if provided.
        """
        if value:
            from core.models import File

            try:
                file_obj = File.objects.get(fid=value)
                return file_obj
            except File.DoesNotExist:
                raise serializers.ValidationError("The specified file does not exist.")
        return value

    def validate_issued_at(self, value):
        """
        Validate that the issued_at date is not in the future.
        """
        if value and value > timezone.now():
            raise serializers.ValidationError("issued_at cannot be in the future.")
        return value

    def update(self, instance, validated_data):
        """
        Update a credential instance.
        """
        return super().update(instance, validated_data)

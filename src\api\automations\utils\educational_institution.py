import re
from django.db.models import Q
from api.automations.utils.text_processing import (
    levenshtein_distance,
    similarity_ratio,
    normalize_text,
)
from core.models import EducationalInstitution
from typing import List, Optional


def normalize_institution_name(name: str) -> str:
    # Remover frases comunes para no afectar al scoring
    common_prefixes = [
        "universidad nacional",
        "universidad privada",
        "universidad",
        "instituto",
        "escuela superior",
        "escuela",
    ]
    name = name.strip()
    for prefix in common_prefixes:
        pattern = rf"^{prefix}\b\s*"
        name = re.sub(pattern, "", name, flags=re.IGNORECASE)
    return normalize_text(name)


def calculate_similarity_score(
    query: str, target: str, is_acronym: bool = False
) -> float:
    """
    Calcula el puntaje de similitud entre query y target
    """
    normalized_query = normalize_institution_name(query)
    normalized_target = normalize_institution_name(target)

    # Para acrónimos, dar mayor peso a coincidencias exactas
    if is_acronym and normalized_query == normalized_target:
        return 1.0

    # Similitud usando SequenceMatcher
    similarity = similarity_ratio(normalized_query, normalized_target)

    # Similitud usando Levenshtein normalizada
    lev_distance = levenshtein_distance(normalized_query, normalized_target)
    max_len = max(len(normalized_query), len(normalized_target))
    lev_similarity = 1 - (lev_distance / max_len) if max_len > 0 else 0

    # Usar la mejor de las dos métricas
    base_score = max(similarity, lev_similarity)

    # Bonus por coincidencia de palabras clave (solo para nombres, no acrónimos)
    if not is_acronym and len(normalized_query) > 3:
        query_words = set(normalized_query.split())
        target_words = set(normalized_target.split())
        common_words = query_words.intersection(target_words)
        if query_words and target_words:
            word_bonus = (
                len(common_words) / max(len(query_words), len(target_words)) * 0.15
            )
            base_score = min(base_score + word_bonus, 1.0)

    return base_score


def get_smart_candidates(
    query: str, max_candidates: int = 50
) -> List[EducationalInstitution]:
    """
    Obtiene candidatos inteligentemente para reducir el conjunto posible de universidades que coinciden con la query
    """
    normalized_query = normalize_institution_name(query)
    query_words = normalized_query.split()

    # Construir Q objects para filtrar candidatos prometedores
    q_objects = Q()

    # 1. Coincidencias exactas (prioridad más alta)
    q_objects |= Q(acronym__iexact=query.strip())
    q_objects |= Q(name__iexact=query.strip())

    # 2. Coincidencias que empiecen con la query
    q_objects |= Q(acronym__istartswith=query.strip())
    q_objects |= Q(name__istartswith=query.strip())

    # 3. Coincidencias que contengan la query completa
    q_objects |= Q(name__icontains=query.strip())
    q_objects |= Q(acronym__icontains=query.strip())

    # 4. Para queries de múltiples palabras, buscar cada palabra
    if len(query_words) > 1:
        for word in query_words:
            if len(word) > 2:  # Ignorar palabras muy cortas
                q_objects |= Q(name__icontains=word)

    # 5. Para queries cortas (posibles acrónimos), buscar por similitud de longitud
    if len(query.strip()) <= 6:
        q_objects |= Q(acronym__isnull=False)

    # Ejecutar la query y limitar resultados
    candidates = EducationalInstitution.objects.filter(q_objects).distinct()[
        :max_candidates
    ]

    # Si no hay suficientes candidatos y la query es muy específica,
    # incluir algunas instituciones adicionales
    if candidates.count() < 10 and len(query.strip()) > 3:
        additional = EducationalInstitution.objects.exclude(
            eiid__in=candidates.values_list("eiid", flat=True)
        )[:20]
        candidates = list(candidates) + list(additional)

    return candidates


def find_with_django_filter(query: str) -> Optional[EducationalInstitution]:
    """
    Intenta encontrar una coincidencia exacta usando filtros optimizados de Django
    """
    if not query or not query.strip():
        return None

    query_clean = query.strip()

    # 1. Coincidencia exacta por acrónimo (más común para queries cortas)
    if len(query_clean) <= 10:  # Probablemente un acrónimo
        exact_acronym = EducationalInstitution.objects.filter(
            acronym__iexact=query_clean
        ).first()
        if exact_acronym:
            return exact_acronym

    # 2. Coincidencia exacta por nombre
    exact_name = EducationalInstitution.objects.filter(name__iexact=query_clean).first()
    if exact_name:
        return exact_name

    # 3. Coincidencia que empiece con la query
    starts_with = EducationalInstitution.objects.filter(
        Q(name__istartswith=query_clean) | Q(acronym__istartswith=query_clean)
    ).first()

    if starts_with:
        return starts_with

    # 4. Coincidencia parcial (solo si no es muy corta)
    if len(query_clean) > 3:
        partial_match = EducationalInstitution.objects.filter(
            Q(name__icontains=query_clean) | Q(acronym__icontains=query_clean)
        ).first()
        return partial_match

    return None


def find_best_match(
    query: str, confidence_threshold: Optional[float] = None
) -> Optional[EducationalInstitution]:
    """
    Encuentra la mejor coincidencia usando candidatos
    Args:
        query: Texto a buscar
        confidence_threshold: Umbral mínimo de confianza

    Returns:
        EducationalInstitution o None
    """
    if not query or not query.strip():
        return None

    threshold = confidence_threshold
    # PASO 1: Obtener candidatos inteligentemente
    candidates: List[EducationalInstitution] = get_smart_candidates(query)

    if not candidates:
        return None

    best_match = None
    best_score = 0

    # PASO 2: Evaluar solo los candidatos prometedores
    for institution in candidates:
        # Buscar por nombre completo
        if institution.name:
            name_score = calculate_similarity_score(
                query, institution.name, is_acronym=False
            )
            if name_score > best_score:
                best_score = name_score
                best_match = institution

        # Buscar por acrónimo
        if institution.acronym:
            acronym_score = calculate_similarity_score(
                query, institution.acronym, is_acronym=True
            )
            if acronym_score > best_score:
                best_score = acronym_score
                best_match = institution

    # print(
    #     {
    #         "best_match": best_match,
    #         "confidence": best_score,
    #         "query": query,
    #         "candidates_evaluated": len(candidates),
    #     }
    # )

    # Verificar si la confianza es suficiente
    if best_match and best_score >= threshold:
        return best_match

    return None

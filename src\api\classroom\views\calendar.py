from datetime import datetime
from django.utils.timezone import make_aware
from rest_framework import mixins, viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import TokenAuthentication
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from collections import defaultdict

from core.models.event import EventSchedule
from api.classroom.serializers.calendar import EventScheduleSerializer


class CalendarEventScheduleViewSet(mixins.ListModelMixin, viewsets.GenericViewSet):
    """
    ViewSet to list a user's EventSchedules for the calendar.
    Returns events grouped by date (YYYY-MM-DD).
    """

    serializer_class = EventScheduleSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]

    year_param = openapi.Parameter(
        "year",
        openapi.IN_QUERY,
        description="Calendar year (defaults to current year). Example: 2025",
        type=openapi.TYPE_INTEGER,
    )
    month_param = openapi.Parameter(
        "month",
        openapi.IN_QUERY,
        description="Calendar month (1–12, defaults to current month). Example: 9",
        type=openapi.TYPE_INTEGER,
    )

    @swagger_auto_schema(
        manual_parameters=[year_param, month_param],
        responses={200: EventScheduleSerializer(many=True)},
    )
    def list(self, request, *args, **kwargs):
        """List events grouped by date"""
        queryset = self.get_queryset()
        grouped = defaultdict(list)
        for event in queryset:
            date_key = event.start_date.date().isoformat()
            grouped[date_key].append(event)

        serialized_grouped = {
            date: self.get_serializer(events, many=True).data
            for date, events in grouped.items()
        }

        return Response(serialized_grouped, status=status.HTTP_200_OK)

    def get_queryset(self):
        user = self.request.user

        year = int(self.request.query_params.get("year", datetime.now().year))
        month = int(self.request.query_params.get("month", datetime.now().month))

        start_date = make_aware(datetime(year, month, 1))
        if month == 12:
            end_date = make_aware(datetime(year + 1, 1, 1))
        else:
            end_date = make_aware(datetime(year, month + 1, 1))

        return (
            EventSchedule.objects.filter(
                enrollments=user,
                start_date__gte=start_date,
                start_date__lt=end_date,
                deleted=False,
            )
            .select_related("event", "instructor")
            .prefetch_related("partnerships")
        )

"""
Set presence for WhatsApp instance via Evolution API
"""

import logging
from typing import Dict, Any, Literal
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)

PresenceType = Literal["available", "unavailable", "composing", "recording", "paused"]


def set_presence(instance_name: str, presence: PresenceType) -> Dict[str, Any]:
    """
    Set presence status for a WhatsApp instance

    Args:
        instance_name: Name of the instance
        presence: Presence status (available, unavailable, composing, recording, paused)

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
        ValueError: If presence type is invalid
    """
    try:
        valid_presences = [
            "available",
            "unavailable",
            "composing",
            "recording",
            "paused",
        ]
        if presence not in valid_presences:
            raise ValueError(
                f"Invalid presence type. Must be one of: {valid_presences}"
            )

        # Build request body
        body = {"presence": presence}

        # Make the request
        response = evolution_request(
            uri=f"/instance/setPresence/{instance_name}", method="POST", data=body
        )

        logger.info(f"Presence set to '{presence}' for instance '{instance_name}'")
        return response

    except EvolutionAPIError as e:
        logger.error(
            f"Failed to set presence for instance '{instance_name}': {e.message}"
        )
        raise
    except ValueError as e:
        logger.error(f"Invalid presence parameter: {str(e)}")
        raise EvolutionAPIError(f"Invalid parameters: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error setting presence: {str(e)}")
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")

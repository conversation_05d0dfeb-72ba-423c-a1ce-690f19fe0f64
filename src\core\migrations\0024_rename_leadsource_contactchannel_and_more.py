# Generated by Django 5.0.6 on 2025-09-03 20:20

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0023_userpreference"),
    ]

    operations = [
        migrations.RenameModel(
            old_name="LeadSource",
            new_name="ContactChannel",
        ),
        migrations.AlterModelOptions(
            name="contactchannel",
            options={
                "verbose_name": "Contact Channel",
                "verbose_name_plural": "Contact Channels",
            },
        ),
        migrations.RenameField(
            model_name="order",
            old_name="lead_sources",
            new_name="contact_channels",
        ),
        migrations.AlterField(
            model_name="order",
            name="contact_channels",
            field=models.ManyToManyField(
                blank=True,
                related_name="orders",
                to="core.contactchannel",
                verbose_name="Contact Channels",
            ),
        ),
        migrations.CreateModel(
            name="LeadOrigin",
            fields=[
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created At"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
                ("deleted", models.BooleanField(default=False, verbose_name="Deleted")),
                (
                    "deleted_at",
                    models.DateTimeField(
                        blank=True, editable=False, null=True, verbose_name="Deleted At"
                    ),
                ),
                (
                    "loid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=255, verbose_name="Name")),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="Description"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_deleted_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Deleted By",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Updated By",
                    ),
                ),
            ],
            options={
                "verbose_name": "Lead Origin",
                "verbose_name_plural": "Lead Origins",
            },
        ),
        migrations.AddField(
            model_name="order",
            name="lead_origins",
            field=models.ManyToManyField(
                blank=True,
                related_name="orders",
                to="core.leadorigin",
                verbose_name="Lead Origins",
            ),
        ),
    ]

from django.utils import timezone
from rest_framework import serializers
from api.swagger import CustomAutoSchema


class AuditMixin:
    def perform_create(self, serializer):
        """
        Automatically set created_by and updated_by fields when creating a new instance
        """
        save_kwargs = {"created_at": timezone.now(), "updated_at": timezone.now()}

        # Set created_by if the model has this field and user is authenticated
        if (
            hasattr(serializer.Meta.model, "created_by")
            and self.request.user
            and self.request.user.is_authenticated
        ):
            save_kwargs["created_by"] = self.request.user

        serializer.save(**save_kwargs)

    def perform_update(self, serializer):
        """
        Automatically set updated_by field when updating an instance
        """
        save_kwargs = {"updated_at": timezone.now()}

        # Set updated_by if the model has this field and user is authenticated
        if (
            hasattr(serializer.Meta.model, "updated_by")
            and self.request.user
            and self.request.user.is_authenticated
        ):
            save_kwargs["updated_by"] = self.request.user

        serializer.save(**save_kwargs)

    def perform_destroy(self, instance):
        """
        Soft delete: set deleted=True, deleted_at and deleted_by instead of hard delete
        """
        if self.request.user and self.request.user.is_authenticated:
            instance.deleted_by = self.request.user
        instance.deleted = True
        instance.deleted_at = timezone.now()
        instance.save()


class KeyMixin(serializers.ModelSerializer):
    key = serializers.SerializerMethodField()

    def get_key(self, obj):
        return obj.id

    class Meta:
        abstract = True


class SwaggerTagMixin:
    swagger_schema = CustomAutoSchema
    swagger_tags = []

    @classmethod
    def as_view(cls, actions=None, **initkwargs):
        view = super().as_view(actions, **initkwargs)
        view.cls.swagger_tags = cls.swagger_tags
        return view


class MultiTenantMixin:
    tenant_configs = {}

    def get_queryset(self):
        tenant = self.request.query_params.get("tenant", None)
        if tenant in self.tenant_configs:
            return self.tenant_configs[tenant]["queryset"](self.queryset)

        return super().get_queryset()

    def get_serializer_class(self):
        tenant = self.request.query_params.get("tenant", None)
        if tenant in self.tenant_configs:
            return self.tenant_configs[tenant]["serializer_class"]
        return super().get_serializer_class()

    def get_permissions(self):
        tenant = self.request.query_params.get("tenant", None)
        if tenant in self.tenant_configs:
            return [
                permission()
                for permission in self.tenant_configs[tenant]["permission_classes"](
                    self
                )
            ]
        return [permission() for permission in self.permission_classes]

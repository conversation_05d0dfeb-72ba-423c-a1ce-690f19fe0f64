import uuid
from django.db import models
from django.utils import timezone
from core.models.base import AuditBaseModel


class EventReminder(AuditBaseModel):
    """
    Event Reminder model for managing dual invitation types (email and WhatsApp)
    for EventScheduleEnrollment instances with independent scheduling and status tracking.
    """

    # Status choices for each invitation type
    PENDING = "PENDING"
    SENT = "SENT"
    FAILED = "FAILED"
    RETRYING = "RETRYING"
    CANCELLED = "CANCELLED"

    STATUS_CHOICES = [
        (PENDING, "Pending"),
        (SENT, "Sent"),
        (FAILED, "Failed"),
        (RETRYING, "Retrying"),
        (CANCELLED, "Cancelled"),
    ]

    rid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )

    # Relationship to EventScheduleEnrollment (nullable for migration compatibility)
    enrollment = models.ForeignKey(
        "EventScheduleEnrollment",
        on_delete=models.CASCADE,
        related_name="event_reminders",
        verbose_name="Event Schedule Enrollment",
        null=True,
        blank=True,
        help_text="The enrollment this reminder is associated with",
    )

    # WhatsApp invitation fields
    status_whatsapp = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default=PENDING,
        verbose_name="WhatsApp Status",
    )
    sent_at_whatsapp = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="WhatsApp Sent At",
        help_text="When the WhatsApp invitation was actually sent",
    )
    last_error_whatsapp = models.TextField(
        null=True,
        blank=True,
        verbose_name="WhatsApp Last Error",
        help_text="Last error message for WhatsApp invitation",
    )
    retry_count_whatsapp = models.PositiveIntegerField(
        default=0,
        verbose_name="WhatsApp Retry Count",
        help_text="Number of retry attempts for WhatsApp invitation",
    )

    # Email invitation fields (email is handled automatically by Google Calendar)
    status_email = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default=PENDING,
        verbose_name="Email Status",
    )
    sent_at_email = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="Email Sent At",
        help_text="When the email invitation was actually sent",
    )
    last_error_email = models.TextField(
        null=True,
        blank=True,
        verbose_name="Email Last Error",
        help_text="Last error message for email invitation",
    )
    retry_count_email = models.PositiveIntegerField(
        default=0,
        verbose_name="Email Retry Count",
        help_text="Number of retry attempts for email invitation",
    )

    def __str__(self):
        event_name = (
            self.enrollment.event_schedule.name if self.enrollment else "Unknown Event"
        )
        user_name = (
            self.enrollment.user.get_full_name()
            if self.enrollment and self.enrollment.user
            else "Unknown User"
        )
        return f"Reminder for {user_name} - {event_name}"

    def is_ready_for_whatsapp_send(self):
        """Check if WhatsApp invitation is ready to be sent"""
        event_schedule = self.enrollment.event_schedule

        if (
            self.status_whatsapp == self.SENT
            or event_schedule.is_whatsapp_active == False
        ):
            return False

        if (
            not event_schedule.scheduled_datetime_whatsapp
            or self.status_whatsapp != self.PENDING
        ):
            return False

        local_tz = timezone.get_current_timezone()
        now = timezone.now().astimezone(local_tz)

        # Send if:
        # 1. Scheduled time has arrived
        # 2. User enrolled after scheduled time (late enrollment)
        # 3. Currently during the event period
        return (
            now >= event_schedule.scheduled_datetime_whatsapp
            or self.enrollment.created_at > event_schedule.scheduled_datetime_whatsapp
            or (event_schedule.start_date <= now <= event_schedule.end_date)
        )

    def is_ready_for_email_send(self):
        """Check if email invitation is ready to be sent"""
        event_schedule = self.enrollment.event_schedule

        if self.status_email == self.SENT:
            return False

        if event_schedule.emails_reminder_auto:
            return True

        if (
            not event_schedule.scheduled_datetime_email
            and not event_schedule.emails_reminder_auto
        ) or self.status_email != self.PENDING:
            return False

        local_tz = timezone.get_current_timezone()
        now = timezone.now().astimezone(local_tz)

        # Send if:
        # 1. Scheduled time has arrived
        # 2. User enrolled after scheduled time (late enrollment)
        # 3. Currently during the event period
        return (
            now >= event_schedule.scheduled_datetime_email
            or self.enrollment.created_at > event_schedule.scheduled_datetime_email
            or (event_schedule.start_date <= now <= event_schedule.end_date)
        )

    class Meta:
        verbose_name = "Event Reminder"
        verbose_name_plural = "Event Reminders"
        indexes = [
            models.Index(fields=["status_whatsapp"]),
            models.Index(fields=["status_email"]),
            models.Index(fields=["enrollment"]),
        ]

import logging
from celery import shared_task
from core.models import Order, StudentEnrollment

logger = logging.getLogger(__name__)


@shared_task
def process_enrollments_for_order(oid):
    """
    Procesa las matrículas para un pedido específico.
    """
    try:
        order = Order.objects.get(oid=oid)
        for order_item in order.items.filter(deleted=False):
            StudentEnrollment.objects.create(
                order_item=order_item,
                user=order.owner,
                is_active=True,
            )
            logger.info(
                f"Enrollment created for user {order.owner} and order item {order_item.id}"
            )
        return {
            "status": "success",
            "message": f"Enrollments processed for order {oid}",
        }
    except Order.DoesNotExist:
        error_msg = f"Order with ID {oid} not found"
        logger.error(error_msg)
        raise Exception(error_msg)
    except Exception as e:
        logger.error(f"Error processing enrollments for order {oid}: {e}")
        raise

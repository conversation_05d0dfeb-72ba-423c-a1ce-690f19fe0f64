from rest_framework import serializers
from core.models import OrderItem, Order, Offering
from api.crm.serializers.offering import CrmOfferingSerializer


class CrmOrderItemOrderSerializer(serializers.ModelSerializer):
    """Serializer for Order information in OrderItem context"""

    class Meta:
        model = Order
        fields = [
            "oid",
            "stage",
            "is_international",
            "has_full_scholarship",
        ]
        extra_kwargs = {
            "oid": {"read_only": True},
            "stage": {"read_only": True},
            "is_international": {"read_only": True},
            "has_full_scholarship": {"read_only": True},
        }


class CrmOrderItemBaseSerializer(serializers.ModelSerializer):
    """Base serializer with common fields for all OrderItem operations"""

    key = serializers.CharField(source="id", read_only=True)
    oiid = serializers.UUIDField(source="id", read_only=True)

    class Meta:
        model = OrderItem
        fields = [
            "key",
            "oiid",
            "order",
            "offering",
            "quantity",
            "custom_amount",
            "base_price",
            "foreign_base_price",
            "discount",
            "unit_price",
            "foreign_unit_price",
            "effective_unit_price",
            "total_price",
            "foreign_total_price",
            "effective_total_price",
            "has_custom_amount",
            "custom_amount_price",
            "ext_invitation_status",
            "team_channel_invitation_status",
            "created_at",
            "updated_at",
        ]


class CrmOrderItemListSerializer(CrmOrderItemBaseSerializer):
    """Serializer for listing order items"""

    order = CrmOrderItemOrderSerializer(read_only=True)
    offering = serializers.SerializerMethodField()

    def get_offering(self, obj):
        """Return basic offering information"""
        return {
            "ofid": obj.offering.ofid,
            "name": obj.offering.name,
            "stage": obj.offering.stage,
        }

    class Meta(CrmOrderItemBaseSerializer.Meta):
        fields = CrmOrderItemBaseSerializer.Meta.fields


class CrmOrderItemRetrieveSerializer(CrmOrderItemBaseSerializer):
    """Serializer for retrieving order item details with full nested relationships"""

    order = CrmOrderItemOrderSerializer(read_only=True)
    offering = CrmOfferingSerializer(read_only=True)

    class Meta(CrmOrderItemBaseSerializer.Meta):
        fields = CrmOrderItemBaseSerializer.Meta.fields


class CrmOrderItemCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating new order items"""

    order = serializers.UUIDField(write_only=True)
    offering = serializers.UUIDField(write_only=True)

    class Meta:
        model = OrderItem
        fields = [
            "order",
            "offering",
            "quantity",
            "custom_amount",
        ]

    def validate_order(self, value):
        """Validate that the order exists and is not deleted"""
        try:
            order = Order.objects.get(oid=value, deleted=False)
            return order
        except Order.DoesNotExist:
            raise serializers.ValidationError(f"La orden con ID {value} no existe")

    def validate_offering(self, value):
        """Validate that the offering exists and is not deleted"""
        try:
            offering = Offering.objects.get(oid=value, deleted=False)
            return offering
        except Offering.DoesNotExist:
            raise serializers.ValidationError(f"La oferta con ID {value} no existe")

    def validate_quantity(self, value):
        """Validate that quantity is positive"""
        if value <= 0:
            raise serializers.ValidationError("La cantidad debe ser mayor a cero.")
        return value

    def validate_custom_amount(self, value):
        """Validate that custom_amount is not negative"""
        if value is not None and value < 0:
            raise serializers.ValidationError(
                "El monto personalizado no puede ser negativo."
            )
        return value


class CrmOrderItemUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating existing order items"""

    class Meta:
        model = OrderItem
        fields = [
            "quantity",
            "custom_amount",
        ]

    def validate_quantity(self, value):
        """Validate that quantity is positive"""
        if value <= 0:
            raise serializers.ValidationError("La cantidad debe ser mayor a cero.")
        return value

    def validate_custom_amount(self, value):
        """Validate that custom_amount is not negative"""
        if value is not None and value < 0:
            raise serializers.ValidationError(
                "El monto personalizado no puede ser negativo."
            )
        return value


class CrmSendClassroomInvitationSerializer(serializers.Serializer):
    """
    Serializer para enviar invitaciones de Google Classroom para un OrderItem específico.
    """

    def validate(self, attrs):
        """
        Valida que el OrderItem tenga los requisitos necesarios para enviar la invitación.
        """
        order_item = self.instance

        if not order_item:
            raise serializers.ValidationError("OrderItem no encontrado")

        # Validar que la orden esté en estado sold
        if order_item.order.stage != order_item.order.SOLD_STAGE:
            raise serializers.ValidationError(
                "La orden debe estar en estado 'sold' para enviar invitaciones"
            )

        # Validar que el offering tenga ext_reference (course_id)
        if not order_item.offering.ext_reference:
            raise serializers.ValidationError(
                "El programa debe tener una referencia externa (course_id) configurada"
            )

        # Validar que el usuario tenga email de Gmail
        owner = order_item.order.owner
        if not owner.email or not owner.email.lower().endswith("@gmail.com"):
            raise serializers.ValidationError(
                "El propietario de la orden debe tener un correo de Gmail válido"
            )

        # Validar que el usuario tenga nombre y apellido
        if not owner.first_name or not owner.last_name:
            raise serializers.ValidationError(
                "El propietario de la orden debe tener nombre y apellido completos"
            )

        return attrs

    def send_invitation(self):
        """
        Envía la invitación de Google Classroom para el OrderItem de forma síncrona.
        """
        order_item = self.instance

        # Importar aquí para evitar importaciones circulares
        from api.crm.tasks.classroom import send_classroom_invitation

        try:
            # Actualizar estado a "enviando"
            order_item.ext_invitation_status = OrderItem.INVITATION_SENDING
            order_item.save(update_fields=["ext_invitation_status"])

            # Enviar la invitación de forma síncrona (sin delay)
            result = send_classroom_invitation(
                email=order_item.order.owner.email,
                class_code=order_item.offering.ext_reference,
                order_item_id=str(order_item.id),
            )

            return {
                "success": True,
                "message": "Invitación enviada exitosamente",
                "order_item_id": str(order_item.id),
                "course_id": order_item.offering.ext_reference,
                "user_email": order_item.order.owner.email,
                "result": result,
            }

        except Exception as e:
            # Actualizar estado a "error"
            order_item.ext_invitation_status = OrderItem.INVITATION_ERROR
            order_item.save(update_fields=["ext_invitation_status"])

            return {
                "success": False,
                "message": f"Error al enviar invitación: {str(e)}",
                "order_item_id": str(order_item.id),
            }


class CrmSendTeamChannelInvitationSerializer(serializers.Serializer):
    """
    Serializer para enviar invitaciones de Team Channel para un OrderItem específico.
    """

    def validate(self, attrs):
        """
        Validar que el OrderItem tenga los datos necesarios para enviar la invitación.
        """
        order_item = self.instance

        if not order_item:
            raise serializers.ValidationError("OrderItem no encontrado")

        # Validar que la orden esté vendida
        if order_item.order.stage != order_item.order.SOLD_STAGE:
            raise serializers.ValidationError(
                "Solo se pueden enviar invitaciones para órdenes vendidas."
            )

        # Validar que el offering tenga team_channel_id
        if not order_item.offering.team_channel_id:
            raise serializers.ValidationError(
                f"El producto '{order_item.offering.name}' no tiene canal de equipo configurado."
            )

        # Validar que el owner tenga número de teléfono
        if not order_item.order.owner.phone_number:
            raise serializers.ValidationError(
                "El propietario de la orden debe tener un número de teléfono."
            )

        # Validar que se pueda reintentar (solo si está en error o forbidden)
        if (
            order_item.team_channel_invitation_status
            == OrderItem.TEAM_CHANNEL_INVITATION_SUCCESS
        ):
            raise serializers.ValidationError("El contacto ya se ha añadido al grupo.")

        return attrs

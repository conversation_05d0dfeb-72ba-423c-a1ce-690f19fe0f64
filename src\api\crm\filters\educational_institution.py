from django_filters import rest_framework as filters
from django.db.models import Q
from core.models import EducationalInstitution


class CrmEducationalInstitutionFilter(filters.FilterSet):
    """Filter for EducationalInstitution model"""

    search = filters.CharFilter(
        method="filter_search",
        help_text="Filter by institution name, acronym, city, or country.",
    )

    type = filters.CharFilter(
        method="filter_type",
        help_text="Filter by institution type, comma separated list of type IDs.",
    )

    created_at = filters.DateFromToRangeFilter()

    class Meta:
        model = EducationalInstitution
        fields = ["search", "type", "created_at"]

    def filter_search(self, queryset, name, value):
        """Filter by name, acronym, city, or country."""
        if not value:
            return queryset

        return queryset.filter(
            Q(name__icontains=value)
            | Q(acronym__icontains=value)
            | Q(city__icontains=value)
            | Q(country__icontains=value)
        )

    def filter_type(self, queryset, name, value):
        """Filter by institution type, comma separated list of type IDs."""
        if not value:
            return queryset

        types = [t.strip() for t in value.split(",") if t.strip()]

        if not types:
            return queryset

        return queryset.filter(institution_type__in=types)

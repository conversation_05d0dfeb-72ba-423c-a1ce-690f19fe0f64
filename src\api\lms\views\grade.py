from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from core.models import Grade
from api.mixins import SwaggerTagMixin
from api.permissions import IsStaffUser
from api.lms.serializers.grade import LmsGradeSerializer
from api.paginations import StandardResultsPagination


class LmsGradeViewSet(
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    """
    ViewSet for managing student enrollments in the LMS.
    """

    queryset = Grade.objects.all()
    serializer_class = LmsGradeSerializer
    pagination_class = StandardResultsPagination
    filter_backends = (DjangoFilterBackend,)
    filterset_fields = ["offering"]

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]

    swagger_tags = ["Grades"]

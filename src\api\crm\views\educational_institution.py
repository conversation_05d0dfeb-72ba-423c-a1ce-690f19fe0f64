from rest_framework import viewsets
from core.models import EducationalInstitution
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.serializers.educational_institution import (
    BaseEducationalInstitutionSerializer,
    RetrieveEducationalInstitutionSerializer,
    CreateEducationalInstitutionSerializer,
    UpdateEducationalInstitutionSerializer,
)
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser
from api.crm.filters.educational_institution import CrmEducationalInstitutionFilter


class CrmEducationalInstitutionViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = EducationalInstitution
    queryset = EducationalInstitution.objects.filter(deleted=False).order_by(
        "created_at"
    )
    swagger_tags = ["Universities"]
    pagination_class = StandardResultsPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]

    filterset_class = CrmEducationalInstitutionFilter
    ordering_fields = ["name", "created_at", "updated_at"]
    ordering = ["-created_at"]

    def get_serializer_class(self):
        if self.action == "list":
            return BaseEducationalInstitutionSerializer
        elif self.action == "retrieve":
            return RetrieveEducationalInstitutionSerializer
        elif self.action == "create":
            return CreateEducationalInstitutionSerializer
        elif self.action in ["update", "partial_update"]:
            return UpdateEducationalInstitutionSerializer
        return super().get_serializer_class()

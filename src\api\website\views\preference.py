from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import TokenAuthentication
from rest_framework.exceptions import PermissionDenied

from core.models import UserPreference
from api.website.serializers.preference import UserPreferenceSerializer


class UserPreferenceViewSet(viewsets.ViewSet):
    """
    ViewSet para recuperar y actualizar las preferencias del usuario autenticado mediante su UID.
    """

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]
    lookup_field = "uid"

    def retrieve(self, request, uid=None):
        """
        GET /api/v1/website/preferences/<uid>
        """
        if str(request.user.uid) != str(uid):
            raise PermissionDenied(
                "No tienes permiso para acceder a estas preferencias."
            )

        preferences, _ = UserPreference.objects.get_or_create(user=request.user)
        serializer = UserPreferenceSerializer(preferences)
        return Response(serializer.data)

    def partial_update(self, request, uid=None):
        """
        PATCH /api/v1/website/preferences/<uid>
        """
        if str(request.user.uid) != str(uid):
            raise PermissionDenied(
                "No tienes permiso para modificar estas preferencias."
            )

        preferences, _ = UserPreference.objects.get_or_create(user=request.user)
        serializer = UserPreferenceSerializer(
            preferences, data=request.data, partial=True
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

from celery import shared_task
from django.core.mail import EmailMessage
from django.conf import settings
from django.template.loader import render_to_string
from core.models import User
from django.utils.timezone import now
from core.models import EventReminder


@shared_task
def send_welcome_email(user_uid, email):
    """
    Task para enviar email de bienvenida después del registro
    """
    try:
        # Obtener el usuario
        user = User.objects.get(uid=user_uid)

        context = {
            "first_name": user.first_name,
            "last_name": user.last_name,
            "app_host": settings.APP_HOST,
        }

        html_content = render_to_string("emails/welcome.html", context)

        email_message = EmailMessage(
            subject=f"¡Bienvenido(a) a CEU, {user.first_name}!",
            body=html_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[email],
            reply_to=[settings.DEFAULT_FROM_EMAIL],
        )

        email_message.content_subtype = "html"
        email_message.send(fail_silently=False)
        return True
    except User.DoesNotExist:
        raise Exception(f"Usuario con UID {user_uid} no encontrado")
    except Exception as e:
        raise e


@shared_task
def schedule_reminders():
    """
    DEPRECATED: This function uses the old EventReminder system
    Use the new invitation system instead: src/api/crm/tasks/event_invitations.py
    This is kept for backward compatibility with Celery Beat schedule
    """
    # Redirect to new system
    from api.crm.tasks.event_invitations import schedule_pending_invitations

    return schedule_pending_invitations.delay()

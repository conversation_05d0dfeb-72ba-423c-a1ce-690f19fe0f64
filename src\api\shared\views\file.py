import uuid
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import Is<PERSON><PERSON><PERSON>icated, IsAdmin<PERSON>ser, AllowAny
from core.models import File
from api.shared.serializers.file import (
    FileSerializer,
    FileUploadSerializer,
    FileUpdateSerializer,
)
from api.utils import perform_create_image_file
from core.utils import delete_file_from_bucket
from api.mixins import AuditMixin, SwaggerTagMixin
from api.utils import upload_image_to_bucket
from django.conf import settings


class SharedFileViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    """
    ViewSet for handling file operations.

    Provides endpoints for uploading, retrieving, updating, and deleting files.
    """

    model_class = File
    queryset = File.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = FileSerializer
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsAdminUser]

    swagger_tags = ["Files"]

    def get_serializer_class(self):
        if self.action == "create" or self.action == "upload":
            return FileUploadSerializer
        if self.action in ["update", "partial_update"]:
            return FileUpdateSerializer
        return super().get_serializer_class()

    def create(self, request, *args, **kwargs):
        """
        Upload a new file.
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        file = perform_create_image_file(
            validated_data.get("file"),
            validated_data.get("width", 500),
            validated_data.get("height", 500),
            validated_data.get("output_format", "WEBP"),
        )

        # Set additional fields and save
        file.name = validated_data["file"].name
        file.description = validated_data.get("description")
        file.is_used = validated_data.get("is_used", False)
        file.save()

        return Response(FileSerializer(file).data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        """
        Update an existing file.

        If a new file is provided, the old file will be replaced while keeping the same ID.
        """
        instance = self.get_object()
        serializer = self.get_serializer(
            instance, data=request.data, partial=kwargs.get("partial", False)
        )
        serializer.is_valid(raise_exception=True)

        # Check if a new file is provided
        if "file" in serializer.validated_data:
            # Delete the old file from storage
            try:
                delete_file_from_bucket(instance.bucket_name, instance.object_name)
            except Exception as e:
                return Response(
                    {"error": f"Failed to delete old file from storage: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            file_data = serializer.validated_data.pop("file")
            width = serializer.validated_data.pop("width", instance.width or 500)
            height = serializer.validated_data.pop("height", instance.height or 500)
            output_format = serializer.validated_data.pop("output_format", "WEBP")

            # Upload the new image
            fid = uuid.uuid4()
            uploaded_image = upload_image_to_bucket(
                fid=fid,
                image=file_data,
                width=width,
                height=height,
                output_format=output_format,
                bucket_name=settings.MINIO_PUBLIC_BUCKET,
            )

            # Update instance fields
            instance.name = (uploaded_image["name"],)
            instance.bucket_name = (uploaded_image["bucket_name"],)
            instance.object_name = (uploaded_image["object_name"],)
            instance.width = (uploaded_image["width"],)
            instance.height = (uploaded_image["height"],)

        # Update other fields
        for attr, value in serializer.validated_data.items():
            setattr(instance, attr, value)

        instance.save()

        return Response(FileSerializer(instance).data, status=status.HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        """
        Delete a file.

        This will mark the file as deleted in the database and remove it from the storage.
        """
        instance = self.get_object()

        # First, delete the file from the storage
        try:
            pass
            # delete_file_from_bucket(instance.bucket_name, instance.object_name)
        except Exception as e:
            return Response(
                {"error": f"Failed to delete file from storage: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        # Then mark it as deleted in the database
        instance.delete()

        return Response(status=status.HTTP_204_NO_CONTENT)

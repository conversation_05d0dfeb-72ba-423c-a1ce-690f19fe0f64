from rest_framework import mixins, viewsets
from rest_framework.response import Response
from api.mixins import SwaggerTagMixin
from core.models import Credential
from api.website.serializers.credential import WebsiteCredentialListSerializer


class WebsiteCredentialViewSet(
    SwaggerTagMixin,
    mixins.RetrieveModelMixin,
    viewsets.GenericViewSet,
):
    model_class = Credential
    queryset = Credential.objects.filter(deleted=False)
    serializer_class = WebsiteCredentialListSerializer

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

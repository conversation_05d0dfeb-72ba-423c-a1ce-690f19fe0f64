from __future__ import absolute_import, unicode_literals
import os
from celery import Celery
from .celery_schedules import CELERYBEAT_SCHEDULE


os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings")

app = Celery("core")
app.config_from_object("django.conf:settings", namespace="CELERY")
app.conf.beat_schedule = CELERYBEAT_SCHEDULE

# Autodiscover tasks from Django apps
app.autodiscover_tasks()

# Explicitly include our custom task modules
app.autodiscover_tasks(
    [
        "api.crm.tasks.classroom",
        "api.crm.tasks.event_invitations",
        "api.crm.tasks.event_invitations.core",
        "api.crm.tasks.event_invitations.whatsapp",
        "api.crm.tasks.event_invitations.email",
        "api.crm.tasks.event_invitations.retry",
        "api.website.tasks.notification",
        "api.lms.tasks.generate_certificate",
        "api.lms.tasks.attach_certificate",
        "api.lms.tasks.send_certificate",
    ]
)


@app.task(bind=True, ignore_result=True)
def debug_task(self):
    print(f"Request: {self.request!r}")

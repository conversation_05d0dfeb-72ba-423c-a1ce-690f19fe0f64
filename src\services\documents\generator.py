from typing import Dict, Any
from django.template.loader import render_to_string
from weasyprint import HTML, CSS
from django.conf import settings
import os


class DocumentGenerator:

    def __init__(self, template_name: str, css_files: list[str] = None):
        """
        Args:
        template_name: Name of the HTML template (must be in templates/documents/)
        css_files: List of CSS files to apply (must be in static/css/documents/)
        """
        self.template_name = f"documents/{template_name}"
        self.css_files = css_files or []

    def _get_css_paths(self) -> list[str]:
        """Gets the full paths of CSS files."""
        css_dir = os.path.join(settings.STATIC_ROOT, "css", "documents")
        return [os.path.join(css_dir, css_file) for css_file in self.css_files]

    def generate_pdf(self, context: Dict[str, Any] = None) -> bytes:
        # Render the HTML template with the context
        html_content = render_to_string(self.template_name, context or {})

        # Create WeasyPrint HTML Object
        html = HTML(string=html_content, base_url=settings.STATIC_ROOT)

        # Load CSS files
        css_files = [CSS(filename=css_path) for css_path in self._get_css_paths()]

        # Generar PDF
        return html.write_pdf(stylesheets=css_files)

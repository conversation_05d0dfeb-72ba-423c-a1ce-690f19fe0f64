"""
Restart WhatsApp instance via Evolution API
"""

import logging
from typing import Dict, Any
from ...evolution_request import evolution_request, EvolutionAPIError

logger = logging.getLogger(__name__)


def restart_instance(instance_name: str) -> Dict[str, Any]:
    """
    Restart a WhatsApp instance

    Args:
        instance_name: Name of the instance to restart

    Returns:
        Dict containing the API response

    Raises:
        EvolutionAPIError: If the request fails
    """
    try:
        # Make the request
        response = evolution_request(
            uri=f"/instance/restart/{instance_name}", method="POST"
        )

        logger.info(f"Instance '{instance_name}' restarted successfully")
        return response

    except EvolutionAPIError as e:
        logger.error(f"Failed to restart instance '{instance_name}': {e.message}")
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error restarting instance '{instance_name}': {str(e)}"
        )
        raise EvolutionAPIError(f"Unexpected error: {str(e)}")

from rest_framework import viewsets
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from core.models import Credential
from api.mixins import AuditMixin, SwaggerTagMixin
from api.permissions import IsStaffUser
from api.lms.serializers.credential import (
    LmsBaseCredentialSerializer,
    LmsCredentialListSerializer,
    LmsCredentialRetrieveSerializer,
    LmsCredentialCreateSerializer,
    LmsCredentialUpdateSerializer,
)
from api.paginations import StandardResultsPagination


class LmsCredentialViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    """
    ViewSet for managing student enrollments in the LMS.
    """

    queryset = Credential.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = LmsBaseCredentialSerializer
    pagination_class = StandardResultsPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]

    swagger_tags = ["Credentials"]

    def get_serializer_class(self):
        if self.action == "create":
            return LmsCredentialCreateSerializer
        elif self.action == "list":
            return LmsCredentialListSerializer
        elif self.action == "retrieve":
            return LmsCredentialRetrieveSerializer
        elif self.action in ["partial_update", "update"]:
            return LmsCredentialUpdateSerializer
        return super().get_serializer_class()

import uuid
from storage.minio import MinioStorage
from rest_framework.exceptions import APIException, ValidationError


def upload_file_to_minio(file_obj, bucket_name="private"):
    try:
        # ✅ Validar tamaño máximo (5MB)
        max_size = 5 * 1024 * 1024  # 5 megabytes
        if file_obj.size > max_size:
            raise ValidationError("El tamaño máximo permitido para la imagen es 5MB.")

        # ✅ Validar tipo de contenido permitido
        allowed_types = {
            "image/jpeg": "jpg",
            "image/png": "png",
            "image/webp": "webp",
        }

        file_extension = allowed_types.get(file_obj.content_type)
        if not file_extension:
            raise ValidationError(
                f"Formato no permitido. Solo se aceptan: {', '.join(allowed_types.values())}"
            )

        # Nombre del archivo con UUID + extensión
        fid = uuid.uuid4()
        object_name = f"{fid}/{file_obj.name.lower()}"

        # Subir a MinIO
        minio = MinioStorage()
        minio.upload(
            bucket_name=bucket_name,
            object_name=object_name,
            data=file_obj,
            length=file_obj.size,
            content_type=file_obj.content_type,
        )

        return fid, object_name
    except (ValidationError, Exception) as e:
        raise APIException(e)

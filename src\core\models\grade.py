from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal


class Grade(models.Model):
    """
    Model representing a grade or score associated to a offering.
    """

    title = models.CharField(
        blank=False,
        null=False,
        max_length=255,
        verbose_name="Title",
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name="Description",
    )
    weight = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        verbose_name="Weight",
    )
    max_points = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal("20.00"),
        verbose_name="Max Score",
        validators=[
            MinValueValidator(Decimal("0.00")),
            MaxValueValidator(Decimal("100.00")),
        ],
    )
    offering = models.ForeignKey(
        "core.Offering",
        on_delete=models.CASCADE,
        related_name="grades",
        verbose_name="Offering",
    )

    @property
    def is_weighted(self):
        return self.weight > 0

    class Meta:
        verbose_name = "Grade"
        verbose_name_plural = "Grades"


class EnrollmentGrade(models.Model):
    """
    Model representing the grade or score obtained by a student in a specific grade item.
    """

    enrollment = models.ForeignKey(
        "core.StudentEnrollment",
        on_delete=models.CASCADE,
        related_name="enrollment_grades",
        verbose_name="Student Enrollment",
    )
    grade = models.ForeignKey(
        "core.Grade",
        on_delete=models.CASCADE,
        related_name="enrollment_grades",
        verbose_name="Grade",
    )
    score = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name="Score",
        validators=[MinValueValidator(0), MaxValueValidator(100)],
    )
    feedback = models.TextField(
        blank=True,
        null=True,
        verbose_name="Feedback",
    )

    class Meta:
        verbose_name = "Enrollment Grade"
        verbose_name_plural = "Enrollment Grades"
        unique_together = ("enrollment", "grade")

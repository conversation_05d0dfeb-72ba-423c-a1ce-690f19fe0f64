from rest_framework import viewsets
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from core.models import Term
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.serializers.term import CrmTermSerializer
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser


class CrmTermViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Term
    queryset = Term.objects.filter(deleted=False)
    serializer_class = CrmTermSerializer
    pagination_class = StandardResultsPagination
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]

    swagger_tags = ["Terms"]

    # Backend filtering & ordering (parity with majors)
    filter_backends = [SearchFilter, OrderingFilter]
    search_fields = ["name"]
    ordering_fields = ["name", "created_at", "id"]
    ordering = ["name"]

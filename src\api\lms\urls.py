from rest_framework import routers
from api.lms.views.enrollment import LmsEnrollmentViewSet
from api.lms.views.credential import LmsCredentialViewSet
from api.lms.views.offering import LmsOfferingViewSet
from api.lms.views.team_channel import TeamChannelViewSet
from api.lms.views.grade import LmsGradeViewSet
from api.lms.views.enrollment_grade import LmsEnrollmentGradeViewSet

router = routers.DefaultRouter(trailing_slash=False)

router.register(
    r"enrollments",
    LmsEnrollmentViewSet,
    basename="lms-enrollment",
)

router.register(
    r"credentials",
    LmsCredentialViewSet,
    basename="lms-credential",
)

router.register(
    r"offerings",
    LmsOfferingViewSet,
    basename="lms-offering",
)

router.register(
    r"grades",
    LmsGradeViewSet,
    basename="lms-grade",
)

router.register(
    r"enrollment-grades",
    LmsEnrollmentGradeViewSet,
    basename="lms-enrollment-grade",
)

router.register(
    r"team-channels",
    TeamChannelViewSet,
    basename="team-channel",
)

urlpatterns = router.urls

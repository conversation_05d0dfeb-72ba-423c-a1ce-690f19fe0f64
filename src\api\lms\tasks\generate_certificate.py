import uuid
import logging
from io import BytesIO
from celery import shared_task
from django.db import transaction
from django.utils import timezone
from django.conf import settings
from api.lms.lib.generate_certificate import generate_certificate, format_date
from core.models import StudentEnrollment, File, Credential
from storage.minio import MinioStorage
from api.lms.lib.enrollment_grade import get_weighted_average_score
from api.lms.services.file import add_csv_mark_to_pdf

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def generate_student_certificate(self, enrollment_id):
    """
    Tarea asíncrona para generar el certificado de un estudiante.

    Args:
        enrollment_id: UUID del StudentEnrollment
    """
    try:
        # Actualizar estado de la tarea
        self.update_state(
            state="PROGRESS",
            meta={"message": "Iniciando..."},
        )

        enrollment = StudentEnrollment.objects.get(eid=enrollment_id)

        with transaction.atomic():
            # Generar el PDF
            self.update_state(
                state="PROGRESS",
                meta={"message": "Generando PDF..."},
            )

            offering = enrollment.order_item.offering
            user = enrollment.user

            student_fullname = f"{user.first_name} {user.last_name}"

            score_value = get_weighted_average_score(enrollment)
            if score_value is not None:
                score_value = round(score_value, 2)

            pdf_content = generate_certificate(
                student_fullname=student_fullname,
                offering_longname=(
                    offering.long_name if offering.long_name else offering.name
                ),
                start_date=(
                    format_date(offering.start_date)
                    if offering.start_date
                    else "No especificada"
                ),
                end_date=(
                    format_date(offering.end_date)
                    if offering.end_date
                    else "No especificada"
                ),
                hours=offering.hours if offering.hours else "__",
                score=score_value,
            )

            cid = str(uuid.uuid4())
            if enrollment.certificate and not enrollment.certificate.deleted:
                cid = str(enrollment.certificate.cid)

            # Wrap in BytesIO since add_csv_mark_to_pdf expects file-like object
            pdf_file_like = BytesIO(pdf_content)
            pdf_buffer = add_csv_mark_to_pdf(
                pdf_file_like,
                cid,
            )
            pdf_content = pdf_buffer.getvalue()

            # Crear el archivo en el bucket
            self.update_state(state="SAVING")

            file_uuid = str(uuid.uuid4())
            object_name = f"{file_uuid}/{student_fullname}.pdf"

            # Crear el registro File
            file = File.objects.create(
                bucket_name=settings.MINIO_PRIVATE_BUCKET,
                object_name=object_name,
                name=f"certificate_{enrollment_id}.pdf",
                is_private=True,
            )

            # Subir el archivo al bucket usando MinioStorage
            pdf_bytes = BytesIO(pdf_content)
            storage = MinioStorage()
            storage.upload(
                bucket_name=settings.MINIO_PRIVATE_BUCKET,
                object_name=object_name,
                data=pdf_bytes,
                length=len(pdf_content),
                content_type="application/pdf",
            )

            # Crear el credential y asociarlo al enrollment, si no tiene ya uno
            if enrollment.certificate and not enrollment.certificate.deleted:
                credential = enrollment.certificate
                if credential.file and not credential.file.deleted:
                    credential.file.delete()
                credential.file = file
                credential.issued_at = timezone.now()
                credential.has_expiration = False
                credential.expires_at = None
                credential.save()
            else:

                credential = Credential.objects.create(
                    cid=cid, file=file, issued_at=timezone.now(), has_expiration=False
                )

            # Asociar el credential al enrollment
            enrollment.certificate = credential
            enrollment.certificate_issued = True
            enrollment.save()

            # Actualizar estado de la tarea
            self.update_state(state="COMPLETED")

            return {
                "status": "success",
                "enrollment_id": str(enrollment.eid),
                "credential_id": str(credential.cid),
                "file_id": str(file.fid),
            }

    except Exception as e:
        logger.error(
            f"Error generando certificado para enrollment {enrollment_id}: {str(e)}"
        )
        # Actualizar estado de la tarea
        self.update_state(
            state="FAILED", meta={"exc_type": type(e).__name__, "exc_message": str(e)}
        )
        raise

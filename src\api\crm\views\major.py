from rest_framework import viewsets
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON>, OrderingFilter
from core.models import Major
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.serializers.major import (
    CrmMajorSerializer,
)
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser


class CrmMajorViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Major
    queryset = Major.objects.filter(deleted=False)
    swagger_tags = ["Universities"]
    serializer_class = CrmMajorSerializer
    pagination_class = StandardResultsPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]

    # Backend filtering & ordering
    filter_backends = [SearchFilter, OrderingFilter]
    search_fields = ["name", "mid"]
    ordering_fields = ["name", "created_at", "id"]
    ordering = ["name"]

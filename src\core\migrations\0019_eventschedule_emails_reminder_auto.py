# Generated by Django 5.0.6 on 2025-08-08 16:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0018_remove_eventreminder_event_alliance_id_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="eventschedule",
            name="emails_reminder_auto",
            field=models.BooleanField(
                blank=True,
                default=True,
                help_text="If enabled, email notifications for new enrollments in this event schedule are sent automatically and instantly. If disabled, you must configure 'Email Scheduled DateTime' to schedule when notifications are sent.",
                verbose_name="Emails Reminder Auto",
            ),
        ),
    ]

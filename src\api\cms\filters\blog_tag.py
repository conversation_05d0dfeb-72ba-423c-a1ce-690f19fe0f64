from django_filters import rest_framework as filters
from core.models import BlogTag
from django.db.models import Q


class CmsBlogTagFilter(filters.FilterSet):
    search = filters.CharFilter(method="filter_search", label="Search by name")

    created_at = filters.DateFromToRangeFilter()

    def filter_search(self, queryset, _, value):
        return queryset.filter(
            Q(name__icontains=value) | Q(description__icontains=value)
        )

    class Meta:
        model = BlogTag
        fields = ["created_at"]

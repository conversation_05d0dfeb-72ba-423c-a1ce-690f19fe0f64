"""
Base classes for event invitation services using Strategy pattern
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class InvitationResult:
    """Result of an invitation sending operation"""

    success: bool
    message: str
    external_id: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    retry_after: Optional[int] = None  # Seconds to wait before retry


class InvitationServiceBase(ABC):
    """
    Abstract base class for invitation services.
    Implements Strategy pattern for different invitation providers.
    """

    @abstractmethod
    def send_invitation(
        self, enrollment, template, variables: Dict[str, Any] = None
    ) -> InvitationResult:
        """
        Send an invitation for the given enrollment.

        Args:
            enrollment: EventScheduleEnrollment instance
            template: Template instance for the invitation
            variables: Additional variables for template rendering

        Returns:
            InvitationResult with success status and details
        """
        pass

    @abstractmethod
    def is_available(self) -> bool:
        """
        Check if the service is currently available.

        Returns:
            True if service is available, False otherwise
        """
        pass

    @abstractmethod
    def get_service_name(self) -> str:
        """
        Get the name of the service.

        Returns:
            Service name as string
        """
        pass

    def validate_template(self, template) -> bool:
        """
        Validate if the template is compatible with this service.

        Args:
            template: Template instance to validate

        Returns:
            True if template is valid for this service
        """
        return template is not None

    def prepare_variables(
        self, enrollment, variables: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Prepare template variables with enrollment data.

        Args:
            enrollment: EventScheduleEnrollment instance
            variables: Additional variables

        Returns:
            Dictionary with all variables for template rendering
        """
        base_variables = {
            "user_first_name": enrollment.first_name
            or (enrollment.user.first_name if enrollment.user else ""),
            "user_last_name": enrollment.last_name
            or (enrollment.user.last_name if enrollment.user else ""),
            "user_email": enrollment.email
            or (enrollment.user.email if enrollment.user else ""),
            "user_phone": enrollment.phone_number
            or (enrollment.user.phone_number if enrollment.user else ""),
            "event_name": enrollment.event_schedule.name,
            "event_start_date": enrollment.event_schedule.start_date,
            "event_end_date": enrollment.event_schedule.end_date,
            "event_description": enrollment.event_schedule.description,
        }

        if variables:
            base_variables.update(variables)

        return base_variables


class ServiceUnavailableError(Exception):
    """Raised when a service is temporarily unavailable"""

    pass


class InvalidTemplateError(Exception):
    """Raised when a template is invalid for the service"""

    pass

from django.db import models
from django.conf import settings


class UserPreference(models.Model):
    """
    Modelo que almacena las preferencias de notificación de un usuario.
    Cada usuario tiene una única instancia asociada que define cómo desea
    recibir notificaciones de la website.
    """

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="preferences",
        help_text="Usuario al que están asociadas estas preferencias.",
    )

    allow_event_invites_whatsapp = models.BooleanField(
        default=True,
        help_text="Permite recibir invitaciones a eventos a través de WhatsApp.",
    )

    notify_by_email = models.BooleanField(
        default=True,
        help_text="Recibir notificaciones generales por correo electrónico.",
    )

    notify_by_push = models.BooleanField(
        default=True,
        help_text="Recibir notificaciones a través de notificaciones push en la app o navegador.",
    )

    notify_security_alerts = models.BooleanField(
        default=True,
        help_text="Recibir alertas de seguridad importantes, como intentos de inicio de sesión sospechosos.",
    )

    def __str__(self):
        return f"Preferencias de {self.user.username}"

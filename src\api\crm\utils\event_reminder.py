"""
Variety of utils for EventReminder model
"""

from django.db.models import Q, Count, Case, When, IntegerField
from core.models import EventReminder
from django.db.models import QuerySet


class CrmEventReminderUtils:
    @staticmethod
    def calculate_metrics(queryset: QuerySet[EventReminder]) -> dict:
        """
        Calculate metrics for EventReminder dashboards
        Args:
            queryset: QuerySet[EventReminder]
        Returns:
            dict:
            {
                "total_reminders": int,
                "total_pending": int,
                "total_sent": int,
                "total_failed": int,
                "whatsapp": {
                    "total_pending": int,
                    "total_sent": int,
                    "total_failed": int,
                },
                "email": {
                    "total_pending": int,
                    "total_sent": int,
                    "total_failed": int,
                },
            }
        """
        total_reminders = queryset.count()
        combined_metrics = queryset.aggregate(
            # A reminder is "pending" if EITHER WhatsApp OR Email is pending
            total_pending=Count(
                Case(
                    When(
                        Q(status_whatsapp=EventReminder.PENDING)
                        | Q(status_email=EventReminder.PENDING),
                        then=1,
                    ),
                    output_field=IntegerField(),
                )
            ),
            # A reminder is "sent" if BOTH WhatsApp AND Email are sent (or not applicable)
            total_sent=Count(
                Case(
                    When(
                        Q(status_whatsapp=EventReminder.SENT)
                        & Q(status_email=EventReminder.SENT),
                        then=1,
                    ),
                    output_field=IntegerField(),
                )
            ),
            # A reminder is "failed" if EITHER WhatsApp OR Email failed (and none pending)
            total_failed=Count(
                Case(
                    When(
                        (
                            Q(status_whatsapp=EventReminder.FAILED)
                            | Q(status_email=EventReminder.FAILED)
                        )
                        & ~Q(status_whatsapp=EventReminder.PENDING)
                        & ~Q(status_email=EventReminder.PENDING),
                        then=1,
                    ),
                    output_field=IntegerField(),
                )
            ),
        )

        # Get WhatsApp metrics using aggregation
        whatsapp_metrics = queryset.aggregate(
            total_pending_whatsapp=Count(
                Case(
                    When(status_whatsapp=EventReminder.PENDING, then=1),
                    output_field=IntegerField(),
                )
            ),
            total_sent_whatsapp=Count(
                Case(
                    When(status_whatsapp=EventReminder.SENT, then=1),
                    output_field=IntegerField(),
                )
            ),
            total_failed_whatsapp=Count(
                Case(
                    When(status_whatsapp=EventReminder.FAILED, then=1),
                    output_field=IntegerField(),
                )
            ),
        )

        # Get Email metrics using aggregation
        email_metrics = queryset.aggregate(
            total_pending_email=Count(
                Case(
                    When(status_email=EventReminder.PENDING, then=1),
                    output_field=IntegerField(),
                )
            ),
            total_sent_email=Count(
                Case(
                    When(status_email=EventReminder.SENT, then=1),
                    output_field=IntegerField(),
                )
            ),
            total_failed_email=Count(
                Case(
                    When(status_email=EventReminder.FAILED, then=1),
                    output_field=IntegerField(),
                )
            ),
        )

        # Calculate totals across both invitation types
        total_pending = combined_metrics["total_pending"]
        total_sent = combined_metrics["total_sent"]
        total_failed = combined_metrics["total_failed"]

        # Prepare data for serializer
        metrics_data = {
            "total_reminders": total_reminders,
            "total_pending": total_pending,
            "total_sent": total_sent,
            "total_failed": total_failed,
            "whatsapp": {
                "total_pending": whatsapp_metrics["total_pending_whatsapp"],
                "total_sent": whatsapp_metrics["total_sent_whatsapp"],
                "total_failed": whatsapp_metrics["total_failed_whatsapp"],
            },
            "email": {
                "total_pending": email_metrics["total_pending_email"],
                "total_sent": email_metrics["total_sent_email"],
                "total_failed": email_metrics["total_failed_email"],
            },
        }

        return metrics_data

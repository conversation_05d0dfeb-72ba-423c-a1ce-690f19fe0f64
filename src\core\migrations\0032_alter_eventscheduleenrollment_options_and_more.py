# Generated by Django 5.0.6 on 2025-09-28 21:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0031_interest_eventscheduleenrollment_user_interests"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="eventscheduleenrollment",
            options={
                "verbose_name": "Event Schedule Enrollment",
                "verbose_name_plural": "Event Schedule Enrollments",
            },
        ),
        migrations.AddField(
            model_name="eventscheduleenrollment",
            name="attended",
            field=models.BooleanField(
                default=False,
                help_text="Indicates if the user attended the event",
                verbose_name="Attended",
            ),
        ),
        migrations.AddIndex(
            model_name="eventscheduleenrollment",
            index=models.Index(fields=["email"], name="event_enrollment_email_idx"),
        ),
        migrations.AddIndex(
            model_name="eventscheduleenrollment",
            index=models.Index(
                fields=["email", "event_schedule"], name="ev_enrollment_email_sched_idx"
            ),
        ),
    ]
